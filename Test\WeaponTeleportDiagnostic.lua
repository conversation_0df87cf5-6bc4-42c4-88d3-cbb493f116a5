--[[
武器瞬间移动问题诊断脚本
专门用于诊断装备远程武器时角色瞬间移动的问题
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local TeleportDiagnostic = {}

-- 诊断状态
local diagnosticState = {
    isMonitoring = false,
    startTime = 0,
    characterPositions = {},
    weaponEvents = {},
    physicsEvents = {},
    lastCharacterPosition = nil,
    monitorConnection = nil,
    teleportDetected = false,
    suspiciousMovements = {}
}

-- 诊断配置
local DIAGNOSTIC_CONFIG = {
    monitorDuration = 10, -- 监控持续时间（秒）
    positionCheckInterval = 0.05, -- 位置检查间隔（秒）
    teleportThreshold = 5.0, -- 瞬移检测阈值（单位距离）
    suspiciousThreshold = 2.0, -- 可疑移动阈值
    maxRecordedPositions = 200 -- 最大记录位置数
}

-- 初始化诊断系统
function TeleportDiagnostic:Initialize()
    print("🔍 武器瞬间移动问题诊断系统已加载")
    print("📋 诊断功能:")
    print("  1. 实时监控角色位置变化")
    print("  2. 检测武器装备事件")
    print("  3. 监控物理属性变化")
    print("  4. 分析瞬移原因")
    print("")
    print("🔑 快捷键:")
    print("  F12 - 开始诊断监控")
    print("  Ctrl+F12 - 停止监控并生成报告")
    
    self:SetupHotkeys()
    self:SetupEventMonitoring()
end

-- 设置快捷键
function TeleportDiagnostic:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F12 then
            if UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
                self:StopMonitoring()
            else
                self:StartMonitoring()
            end
        end
    end)
end

-- 设置事件监控
function TeleportDiagnostic:SetupEventMonitoring()
    local player = Players.LocalPlayer
    
    -- 监控武器装备事件
    if player.Character then
        player.Character.ChildAdded:Connect(function(child)
            if child:IsA("Tool") then
                self:RecordWeaponEvent("EQUIPPED", child.Name, tick())
                print("🔧 检测到武器装备: " .. child.Name)
                
                -- 检查武器Handle的物理属性
                local handle = child:FindFirstChild("Handle")
                if handle then
                    self:CheckHandlePhysics(handle, "EQUIPPED")
                end
            end
        end)
        
        player.Character.ChildRemoved:Connect(function(child)
            if child:IsA("Tool") then
                self:RecordWeaponEvent("UNEQUIPPED", child.Name, tick())
                print("🔧 检测到武器卸载: " .. child.Name)
            end
        end)
    end
end

-- 开始监控
function TeleportDiagnostic:StartMonitoring()
    if diagnosticState.isMonitoring then
        print("⚠️ 监控已在进行中")
        return
    end
    
    print("🚀 开始武器瞬移诊断监控")
    print("=" * 50)
    
    -- 重置状态
    diagnosticState.isMonitoring = true
    diagnosticState.startTime = tick()
    diagnosticState.characterPositions = {}
    diagnosticState.weaponEvents = {}
    diagnosticState.physicsEvents = {}
    diagnosticState.teleportDetected = false
    diagnosticState.suspiciousMovements = {}
    
    -- 记录初始位置
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        diagnosticState.lastCharacterPosition = player.Character.HumanoidRootPart.Position
        self:RecordPosition(diagnosticState.lastCharacterPosition, "INITIAL")
    end
    
    -- 开始位置监控循环
    diagnosticState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorCharacterPosition()
    end)
    
    -- 自动停止监控
    task.spawn(function()
        task.wait(DIAGNOSTIC_CONFIG.monitorDuration)
        if diagnosticState.isMonitoring then
            self:StopMonitoring()
        end
    end)
    
    print("📊 监控已启动，将持续 " .. DIAGNOSTIC_CONFIG.monitorDuration .. " 秒")
    print("💡 现在装备一个远程武器来测试...")
end

-- 停止监控
function TeleportDiagnostic:StopMonitoring()
    if not diagnosticState.isMonitoring then
        print("⚠️ 监控未在进行中")
        return
    end
    
    print("🛑 停止监控")
    diagnosticState.isMonitoring = false
    
    -- 断开连接
    if diagnosticState.monitorConnection then
        diagnosticState.monitorConnection:Disconnect()
        diagnosticState.monitorConnection = nil
    end
    
    -- 生成诊断报告
    self:GenerateDiagnosticReport()
end

-- 监控角色位置
function TeleportDiagnostic:MonitorCharacterPosition()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local currentPosition = player.Character.HumanoidRootPart.Position
    local currentTime = tick()
    
    if diagnosticState.lastCharacterPosition then
        local movement = (currentPosition - diagnosticState.lastCharacterPosition).Magnitude
        local timeDelta = currentTime - (diagnosticState.characterPositions[#diagnosticState.characterPositions] and 
                                        diagnosticState.characterPositions[#diagnosticState.characterPositions].time or diagnosticState.startTime)
        
        -- 检测瞬移
        if movement > DIAGNOSTIC_CONFIG.teleportThreshold then
            diagnosticState.teleportDetected = true
            self:RecordPosition(currentPosition, "TELEPORT", {
                movement = movement,
                timeDelta = timeDelta,
                speed = movement / math.max(timeDelta, 0.001)
            })
            print("🚨 检测到瞬移! 移动距离: " .. string.format("%.3f", movement) .. " 单位")
        elseif movement > DIAGNOSTIC_CONFIG.suspiciousThreshold then
            table.insert(diagnosticState.suspiciousMovements, {
                time = currentTime,
                movement = movement,
                timeDelta = timeDelta,
                position = currentPosition
            })
            self:RecordPosition(currentPosition, "SUSPICIOUS", {
                movement = movement,
                timeDelta = timeDelta
            })
        else
            self:RecordPosition(currentPosition, "NORMAL")
        end
    end
    
    diagnosticState.lastCharacterPosition = currentPosition
end

-- 记录位置
function TeleportDiagnostic:RecordPosition(position, eventType, extraData)
    local record = {
        time = tick() - diagnosticState.startTime,
        position = position,
        eventType = eventType,
        extraData = extraData or {}
    }
    
    table.insert(diagnosticState.characterPositions, record)
    
    -- 限制记录数量
    if #diagnosticState.characterPositions > DIAGNOSTIC_CONFIG.maxRecordedPositions then
        table.remove(diagnosticState.characterPositions, 1)
    end
end

-- 记录武器事件
function TeleportDiagnostic:RecordWeaponEvent(eventType, weaponName, time)
    table.insert(diagnosticState.weaponEvents, {
        time = time - diagnosticState.startTime,
        eventType = eventType,
        weaponName = weaponName
    })
end

-- 检查Handle物理属性
function TeleportDiagnostic:CheckHandlePhysics(handle, context)
    local physicsData = {
        time = tick() - diagnosticState.startTime,
        context = context,
        handleName = handle.Name,
        anchored = handle.Anchored,
        canCollide = handle.CanCollide,
        canQuery = handle.CanQuery,
        massless = handle.Massless,
        size = handle.Size,
        position = handle.Position,
        cframe = handle.CFrame
    }
    
    table.insert(diagnosticState.physicsEvents, physicsData)
    
    print("🔍 Handle物理属性 (" .. context .. "):")
    print("  - Anchored: " .. tostring(handle.Anchored))
    print("  - CanCollide: " .. tostring(handle.CanCollide))
    print("  - CanQuery: " .. tostring(handle.CanQuery))
    print("  - Massless: " .. tostring(handle.Massless))
    print("  - Position: " .. tostring(handle.Position))
end

-- 生成诊断报告
function TeleportDiagnostic:GenerateDiagnosticReport()
    print("\n" .. "=" * 60)
    print("📊 武器瞬间移动问题诊断报告")
    print("=" * 60)
    
    local monitorDuration = tick() - diagnosticState.startTime
    print("🕐 监控时长: " .. string.format("%.2f", monitorDuration) .. " 秒")
    print("📍 记录位置数: " .. #diagnosticState.characterPositions)
    print("🔧 武器事件数: " .. #diagnosticState.weaponEvents)
    print("⚙️ 物理事件数: " .. #diagnosticState.physicsEvents)
    print("")
    
    -- 瞬移检测结果
    if diagnosticState.teleportDetected then
        print("🚨 瞬移检测结果: 检测到瞬间移动!")
        
        -- 分析瞬移事件
        local teleportEvents = {}
        for _, record in ipairs(diagnosticState.characterPositions) do
            if record.eventType == "TELEPORT" then
                table.insert(teleportEvents, record)
            end
        end
        
        print("📊 瞬移事件详情:")
        for i, event in ipairs(teleportEvents) do
            print("  事件 " .. i .. ":")
            print("    时间: " .. string.format("%.3f", event.time) .. "s")
            print("    移动距离: " .. string.format("%.3f", event.extraData.movement) .. " 单位")
            print("    移动速度: " .. string.format("%.1f", event.extraData.speed) .. " 单位/秒")
            print("    位置: " .. tostring(event.position))
        end
    else
        print("✅ 瞬移检测结果: 未检测到瞬间移动")
    end
    
    -- 可疑移动分析
    if #diagnosticState.suspiciousMovements > 0 then
        print("\n⚠️ 可疑移动事件: " .. #diagnosticState.suspiciousMovements .. " 次")
        for i, movement in ipairs(diagnosticState.suspiciousMovements) do
            if i <= 5 then -- 只显示前5个
                print("  " .. i .. ". 时间: " .. string.format("%.3f", movement.time - diagnosticState.startTime) .. "s, 距离: " .. string.format("%.3f", movement.movement))
            end
        end
    end
    
    -- 武器事件分析
    print("\n🔧 武器事件时间线:")
    for _, event in ipairs(diagnosticState.weaponEvents) do
        print("  " .. string.format("%.3f", event.time) .. "s - " .. event.eventType .. ": " .. event.weaponName)
    end
    
    -- 物理属性分析
    print("\n⚙️ 物理属性变化:")
    for _, physics in ipairs(diagnosticState.physicsEvents) do
        print("  " .. string.format("%.3f", physics.time) .. "s - " .. physics.context .. " (" .. physics.handleName .. "):")
        print("    Anchored: " .. tostring(physics.anchored) .. ", CanCollide: " .. tostring(physics.canCollide))
        print("    Position: " .. tostring(physics.position))
    end
    
    -- 问题分析和建议
    print("\n🔍 问题分析:")
    if diagnosticState.teleportDetected then
        print("❌ 检测到瞬间移动问题")
        print("💡 可能原因:")
        print("  1. 武器Handle的物理属性设置不当")
        print("  2. WeldConstraint创建时的CFrame冲突")
        print("  3. 锚定状态切换时的位置跳跃")
        print("  4. 武器跟随系统的followPart位置计算错误")
        
        print("\n🔧 建议修复方案:")
        print("  1. 检查StartWeaponCameraFollow中的Handle物理设置")
        print("  2. 确保followPart的初始位置正确")
        print("  3. 在创建WeldConstraint前添加位置验证")
        print("  4. 使用更安全的锚定切换方式")
    else
        print("✅ 未检测到瞬间移动问题")
        print("💡 系统运行正常")
    end
    
    -- 详细的CameraControlService状态检查
    print("\n📷 相机控制服务状态:")
    local isFollowActive = CameraControlService:IsWeaponFollowActive()
    local isFirstPerson = CameraControlService:IsFirstPerson()
    print("  武器跟随激活: " .. tostring(isFollowActive))
    print("  第一人称视角: " .. tostring(isFirstPerson))
    
    print("=" * 60)
    print("📋 诊断完成")
    print("💡 如需更详细的分析，请查看上述报告内容")
end

-- 手动触发武器装备测试
function TeleportDiagnostic:TestWeaponEquip()
    print("🧪 手动触发武器装备测试")
    
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return
    end
    
    -- 查找远程武器
    local remoteWeapons = {"HyperlaserGun", "MP5K"}
    
    for _, weaponName in ipairs(remoteWeapons) do
        local weapon = backpack:FindFirstChild(weaponName)
        if weapon then
            print("🔧 装备测试武器: " .. weaponName)
            weapon.Parent = player.Character
            return
        end
    end
    
    print("❌ 背包中没有可用的远程武器")
end

-- 自动初始化
TeleportDiagnostic:Initialize()

return TeleportDiagnostic
