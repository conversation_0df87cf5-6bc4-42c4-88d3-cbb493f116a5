--[[
摄像机控制服务
负责管理玩家的视角控制，包括第一视角和第三视角的切换
以及远程武器跟随鼠标转动功能
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")

-- 引入通知服务
local NotifyService = require(ReplicatedStorage.Scripts.Share.Services.NotifyService)

local CameraControlService = {}

-- 获取本地玩家
local player = Players.LocalPlayer

-- 摄像机设置常量
local FIRST_PERSON_ZOOM = 0.5  -- 第一视角缩放距离
local THIRD_PERSON_ZOOM = 15   -- 第三视角缩放距离

-- 状态变量
local isFirstPerson = true  -- 当前是否为第一视角
local originalCameraSettings = {}  -- 保存原始摄像机设置

-- 武器跟随系统变量
local weaponFollowActive = false  -- 武器跟随是否激活
local currentWeaponTool = nil     -- 当前跟随的武器工具
local weaponFollowConnection = nil -- 武器跟随的连接
local followPart = nil            -- 跟随部件
local weaponWeld = nil           -- 武器焊接约束

-- 武器跟随配置
local WEAPON_FOLLOW_CONFIG = {
	weaponOffset = {
		forward = 2.5,  -- 向前偏移
		right = 0.6,    -- 向右偏移
		up = -0.2       -- 向上偏移
	},
	followSmoothing = 0.25,  -- 位置平滑度
	rotationSmoothing = 0.2, -- 旋转平滑度
	rotationLimits = {
		maxPitch = 75,  -- 最大俯仰角
		minPitch = -75, -- 最小俯仰角
		maxYaw = 120,   -- 最大偏航角
		minYaw = -120   -- 最小偏航角
	}
}

-- 初始化服务
function CameraControlService:Initialize()
	print("CameraControlService 初始化")

	-- 等待玩家加载
	if not player then
		player = Players.PlayerAdded:Wait()
	end

	-- 保存原始摄像机设置
	self:SaveOriginalCameraSettings()

	-- 设置初始第一视角
	self:SetFirstPersonView()

	-- 监听濒死状态变化
	self:SetupDownedStateListener()

	-- 监听角色重生
	player.CharacterAdded:Connect(function(character)
		-- 角色重生时重新设置第一视角
		wait(1) -- 等待角色完全加载
		self:SetFirstPersonView()
	end)

	print("CameraControlService 初始化完成")
end

-- 保存原始摄像机设置
function CameraControlService:SaveOriginalCameraSettings()
	if player then
		originalCameraSettings.CameraMinZoomDistance = player.CameraMinZoomDistance
		originalCameraSettings.CameraMaxZoomDistance = player.CameraMaxZoomDistance
		originalCameraSettings.CameraMode = player.CameraMode

		print("已保存原始摄像机设置:")
		print("  MinZoom:", originalCameraSettings.CameraMinZoomDistance)
		print("  MaxZoom:", originalCameraSettings.CameraMaxZoomDistance)
		print("  CameraMode:", originalCameraSettings.CameraMode)
	end
end

-- 设置第一视角
function CameraControlService:SetFirstPersonView()
	if not player then return end

	-- 设置摄像机缩放距离为相同值，强制第一视角
	player.CameraMinZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMaxZoomDistance = FIRST_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = true

	print("已设置第一视角 (缩放距离: " .. FIRST_PERSON_ZOOM .. ")")
end

-- 设置第三视角
function CameraControlService:SetThirdPersonView()
	if not player then return end

	-- 设置摄像机缩放距离允许第三视角
	player.CameraMinZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMaxZoomDistance = THIRD_PERSON_ZOOM
	player.CameraMode = Enum.CameraMode.Classic

	isFirstPerson = false

	-- 切换到第三视角时停止武器跟随
	if weaponFollowActive then
		self:StopWeaponCameraFollow()
		print("切换到第三视角，已停止武器跟随")
	end

	print("已设置第三视角 (缩放距离: 5-" .. THIRD_PERSON_ZOOM .. ")")
end

-- 恢复原始摄像机设置
function CameraControlService:RestoreOriginalCameraSettings()
	if not player or not originalCameraSettings.CameraMinZoomDistance then return end

	player.CameraMinZoomDistance = originalCameraSettings.CameraMinZoomDistance
	player.CameraMaxZoomDistance = originalCameraSettings.CameraMaxZoomDistance
	player.CameraMode = originalCameraSettings.CameraMode

	print("已恢复原始摄像机设置")
end

-- 设置濒死状态监听
function CameraControlService:SetupDownedStateListener()
	-- 监听濒死状态事件
	NotifyService.RegisterClientEvent("PlayerDowned", function(data)
		print("收到濒死状态事件，切换到第三视角")
		self:SetThirdPersonView()
	end)

	-- 监听复活事件
	NotifyService.RegisterClientEvent("PlayerRevived", function(data)
		print("收到复活事件，切换到第一视角")
		self:SetFirstPersonView()
	end)

	-- 监听玩家属性变化（备用方案）
	if player then
		player.AttributeChanged:Connect(function(attributeName)
			if attributeName == "IsDowned" then
				local isDowned = player:GetAttribute("IsDowned")
				if isDowned then
					print("检测到濒死属性变化，切换到第三视角")
					self:SetThirdPersonView()
				else
					print("检测到复活属性变化，切换到第一视角")
					self:SetFirstPersonView()
				end
			end
		end)
	end
end

-- 获取当前视角状态
function CameraControlService:IsFirstPerson()
	return isFirstPerson
end

-- 手动切换视角（调试用）
function CameraControlService:ToggleView()
	if isFirstPerson then
		self:SetThirdPersonView()
	else
		self:SetFirstPersonView()
	end
end

-- ========== 武器跟随系统 ==========

-- 启动武器跟随功能（安全版本，避免瞬移）
function CameraControlService:StartWeaponCameraFollow(weaponTool)
	-- 验证输入参数
	if not weaponTool or not weaponTool:IsA("Tool") then
		print("❌ 无效的武器工具")
		return false
	end

	-- 检查是否已经在跟随状态
	if weaponFollowActive then
		print("⚠️ 武器跟随已激活，先停止当前跟随")
		self:StopWeaponCameraFollow()
	end

	-- 检查是否为第一人称视角
	if not isFirstPerson then
		print("⚠️ 只有在第一人称视角下才能启动武器跟随")
		return false
	end

	-- 获取武器Handle
	local handle = weaponTool:FindFirstChild("Handle")
	if not handle or not handle:IsA("BasePart") then
		print("❌ 武器缺少有效的Handle")
		return false
	end

	-- 获取角色和相机
	local character = player.Character
	if not character or not character:FindFirstChild("HumanoidRootPart") then
		print("❌ 角色或HumanoidRootPart不存在")
		return false
	end

	local camera = workspace.CurrentCamera
	if not camera then
		print("❌ 相机不存在")
		return false
	end

	print("🎯 启动武器跟随: " .. weaponTool.Name)

	-- 使用延迟初始化避免瞬移
	return self:SafeInitializeWeaponFollow(weaponTool, handle, camera)
end

-- 安全初始化武器跟随（分步骤避免瞬移）
function CameraControlService:SafeInitializeWeaponFollow(weaponTool, handle, camera)
	-- 记录Handle的原始状态
	local originalHandleCFrame = handle.CFrame
	local originalAnchored = handle.Anchored
	local originalCanCollide = handle.CanCollide

	print("🔧 原始Handle状态 - 位置: " .. tostring(handle.Position) .. ", 锚定: " .. tostring(originalAnchored))

	-- 第一步：创建跟随部件，放在Handle的当前位置
	followPart = Instance.new("Part")
	followPart.Name = "WeaponFollowPart"
	followPart.Size = Vector3.new(0.1, 0.1, 0.1)
	followPart.Transparency = 1  -- 完全透明
	followPart.CanCollide = false
	followPart.CanQuery = false
	followPart.Anchored = true  -- 锚定防止物理计算
	followPart.CFrame = originalHandleCFrame  -- 使用Handle的当前位置
	followPart.Parent = workspace

	-- 第二步：安全地修改Handle物理属性
	-- 先锚定Handle，防止在修改属性时产生物理反应
	handle.Anchored = false
	handle.CanCollide = false
	handle.CanQuery = false
	handle.Massless = true

	-- 第三步：创建WeldConstraint
	weaponWeld = Instance.new("WeldConstraint")
	weaponWeld.Part0 = followPart
	weaponWeld.Part1 = handle
	weaponWeld.Parent = followPart

	-- 第四步：保存状态并启动跟随
	weaponFollowActive = true
	currentWeaponTool = weaponTool

	print("🔧 武器跟随初始化完成 - Handle位置: " .. tostring(handle.Position))

	-- 第五步：启动跟随更新循环
	weaponFollowConnection = RunService.Heartbeat:Connect(function()
		self:UpdateWeaponFollow()
	end)

	print("✅ 武器跟随已安全启动")
	return true
end

-- 停止武器跟随功能
function CameraControlService:StopWeaponCameraFollow()
	if not weaponFollowActive then
		return
	end

	print("🛑 停止武器跟随")

	-- 断开更新连接
	if weaponFollowConnection then
		weaponFollowConnection:Disconnect()
		weaponFollowConnection = nil
	end

	-- 清理跟随部件
	if followPart then
		followPart:Destroy()
		followPart = nil
	end

	-- 清理焊接约束
	if weaponWeld then
		weaponWeld:Destroy()
		weaponWeld = nil
	end

	-- 恢复武器Handle的物理属性
	if currentWeaponTool then
		local handle = currentWeaponTool:FindFirstChild("Handle")
		if handle then
			handle.Anchored = false  -- 恢复为非锚定状态
		end
	end

	-- 重置状态
	weaponFollowActive = false
	currentWeaponTool = nil

	print("✅ 武器跟随已停止")
end

-- 检查武器跟随是否激活
function CameraControlService:IsWeaponFollowActive()
	return weaponFollowActive
end

-- 更新武器跟随
function CameraControlService:UpdateWeaponFollow()
	-- 安全检查
	if not weaponFollowActive or not followPart or not currentWeaponTool then
		return
	end

	-- 检查武器是否仍然存在
	if not currentWeaponTool.Parent then
		print("⚠️ 武器已被移除，停止跟随")
		self:StopWeaponCameraFollow()
		return
	end

	-- 检查是否仍为第一人称
	if not isFirstPerson then
		print("⚠️ 切换到第三人称，停止武器跟随")
		self:StopWeaponCameraFollow()
		return
	end

	-- 获取相机
	local camera = workspace.CurrentCamera
	if not camera then
		return
	end

	-- 计算目标位置和旋转
	local targetCFrame = self:CalculateWeaponPosition(camera.CFrame)

	-- 应用平滑插值
	local currentCFrame = followPart.CFrame
	local smoothedCFrame = currentCFrame:Lerp(targetCFrame, WEAPON_FOLLOW_CONFIG.followSmoothing)

	-- 更新跟随部件位置
	followPart.CFrame = smoothedCFrame
end

-- 计算武器位置和旋转
function CameraControlService:CalculateWeaponPosition(cameraCFrame, useCurrentHandlePosition)
	-- 获取相机的方向向量
	local cameraDirection = cameraCFrame.LookVector
	local cameraUp = cameraCFrame.UpVector
	local cameraRight = cameraCFrame.RightVector

	-- 计算俯仰角（上下转动）
	local pitch = math.asin(-cameraDirection.Y)
	pitch = math.deg(pitch)

	-- 应用俯仰角限制
	pitch = math.clamp(pitch, WEAPON_FOLLOW_CONFIG.rotationLimits.minPitch, WEAPON_FOLLOW_CONFIG.rotationLimits.maxPitch)

	-- 重新计算限制后的方向向量
	local limitedPitchRad = math.rad(pitch)
	local limitedDirection = Vector3.new(
		cameraDirection.X,
		-math.sin(limitedPitchRad),
		cameraDirection.Z
	).Unit

	-- 计算武器基础位置
	local basePosition
	if useCurrentHandlePosition and currentWeaponTool then
		-- 初始化时使用Handle的当前位置作为基础，避免瞬移
		local handle = currentWeaponTool:FindFirstChild("Handle")
		if handle then
			basePosition = handle.Position
		else
			basePosition = cameraCFrame.Position
				+ limitedDirection * WEAPON_FOLLOW_CONFIG.weaponOffset.forward
				+ cameraRight * WEAPON_FOLLOW_CONFIG.weaponOffset.right
				+ cameraUp * WEAPON_FOLLOW_CONFIG.weaponOffset.up
		end
	else
		-- 正常跟随时使用相机位置计算
		basePosition = cameraCFrame.Position
			+ limitedDirection * WEAPON_FOLLOW_CONFIG.weaponOffset.forward
			+ cameraRight * WEAPON_FOLLOW_CONFIG.weaponOffset.right
			+ cameraUp * WEAPON_FOLLOW_CONFIG.weaponOffset.up
	end

	-- 构建武器的旋转CFrame
	local weaponCFrame = CFrame.lookAt(basePosition, basePosition + limitedDirection, cameraUp)

	return weaponCFrame
end

-- 设置武器跟随配置
function CameraControlService:SetWeaponFollowConfig(config)
	if config.weaponOffset then
		for key, value in pairs(config.weaponOffset) do
			WEAPON_FOLLOW_CONFIG.weaponOffset[key] = value
		end
	end

	if config.followSmoothing then
		WEAPON_FOLLOW_CONFIG.followSmoothing = config.followSmoothing
	end

	if config.rotationSmoothing then
		WEAPON_FOLLOW_CONFIG.rotationSmoothing = config.rotationSmoothing
	end

	if config.rotationLimits then
		for key, value in pairs(config.rotationLimits) do
			WEAPON_FOLLOW_CONFIG.rotationLimits[key] = value
		end
	end

	print("✅ 武器跟随配置已更新")
end

-- 获取当前武器跟随配置
function CameraControlService:GetWeaponFollowConfig()
	return WEAPON_FOLLOW_CONFIG
end

return CameraControlService
