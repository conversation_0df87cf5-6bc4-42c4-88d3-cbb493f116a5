--[[
武器飞行问题修复验证测试脚本
验证新的BodyPosition方案是否解决了角色飞起来的问题
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local FlyingFixTest = {}

-- 测试状态
local testState = {
    isMonitoring = false,
    startTime = 0,
    characterPositions = {},
    testResults = {
        beforeEquip = nil,
        afterEquip = nil,
        maxHeight = 0,
        minHeight = 0,
        heightVariation = 0,
        flyingDetected = false,
        stableMovement = true
    },
    monitorConnection = nil,
    lastCharacterPosition = nil
}

-- 测试配置
local TEST_CONFIG = {
    monitorDuration = 10, -- 监控持续时间（秒）
    flyingThreshold = 3.0, -- 飞行检测阈值
    stabilityThreshold = 1.0, -- 稳定性阈值
    testWeapons = {"HyperlaserGun", "MP5K"}
}

-- 初始化测试系统
function FlyingFixTest:Initialize()
    print("🔧 武器飞行问题修复验证测试系统已加载")
    print("📋 测试目标:")
    print("  1. 验证装备远程武器时角色不会飞起来")
    print("  2. 确认武器跟随功能正常工作")
    print("  3. 检查角色移动的稳定性")
    print("  4. 验证BodyPosition方案的有效性")
    print("")
    print("🔑 快捷键:")
    print("  F4 - 开始飞行修复验证测试")
    
    self:SetupHotkeys()
    
    -- 初始化相关服务
    WeaponClient:Initialize()
    CameraControlService:Initialize()
end

-- 设置快捷键
function FlyingFixTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F4 then
            self:StartFlyingFixTest()
        end
    end)
end

-- 开始飞行修复验证测试
function FlyingFixTest:StartFlyingFixTest()
    if testState.isMonitoring then
        print("⚠️ 测试已在进行中")
        return
    end
    
    print("🚀 开始武器飞行问题修复验证测试")
    print("=" * 50)
    
    -- 重置测试状态
    testState.isMonitoring = true
    testState.startTime = tick()
    testState.characterPositions = {}
    testState.testResults = {
        beforeEquip = nil,
        afterEquip = nil,
        maxHeight = 0,
        minHeight = math.huge,
        heightVariation = 0,
        flyingDetected = false,
        stableMovement = true
    }
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 记录装备前的位置
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        testState.testResults.beforeEquip = player.Character.HumanoidRootPart.Position
        testState.lastCharacterPosition = testState.testResults.beforeEquip
        testState.testResults.maxHeight = testState.testResults.beforeEquip.Y
        testState.testResults.minHeight = testState.testResults.beforeEquip.Y
        print("📍 装备前角色位置: " .. tostring(testState.testResults.beforeEquip))
    end
    
    -- 开始位置监控
    testState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorCharacterStability()
    end)
    
    -- 装备武器并监控
    task.spawn(function()
        task.wait(1) -- 等待1秒开始监控
        self:EquipTestWeapon()
        
        task.wait(TEST_CONFIG.monitorDuration) -- 监控指定时间
        self:StopTest()
    end)
    
    print("📊 监控已启动，将持续 " .. (TEST_CONFIG.monitorDuration + 1) .. " 秒")
    print("💡 请在测试期间尝试移动角色...")
end

-- 装备测试武器
function FlyingFixTest:EquipTestWeapon()
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    -- 查找测试武器
    for _, weaponName in ipairs(TEST_CONFIG.testWeapons) do
        local weapon = backpack:FindFirstChild(weaponName)
        if weapon then
            print("🔧 装备测试武器: " .. weaponName)
            
            -- 装备武器
            weapon.Parent = player.Character
            
            -- 等待一帧后记录装备后位置
            task.wait()
            if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
                testState.testResults.afterEquip = player.Character.HumanoidRootPart.Position
                print("📍 装备后角色位置: " .. tostring(testState.testResults.afterEquip))
                
                -- 计算装备前后的位移
                if testState.testResults.beforeEquip then
                    local equipMovement = (testState.testResults.afterEquip - testState.testResults.beforeEquip).Magnitude
                    print("📏 装备前后位移: " .. string.format("%.3f", equipMovement) .. " 单位")
                    
                    if equipMovement > TEST_CONFIG.flyingThreshold then
                        testState.testResults.flyingDetected = true
                        print("🚨 检测到装备时异常移动! 位移: " .. string.format("%.3f", equipMovement) .. " 单位")
                    else
                        print("✅ 装备过程正常，无异常移动")
                    end
                end
            end
            
            return true
        end
    end
    
    print("❌ 背包中没有可用的测试武器")
    print("💡 可用武器:")
    for _, child in pairs(backpack:GetChildren()) do
        if child:IsA("Tool") then
            print("  - " .. child.Name)
        end
    end
    return false
end

-- 监控角色稳定性
function FlyingFixTest:MonitorCharacterStability()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local currentPosition = player.Character.HumanoidRootPart.Position
    local currentTime = tick()
    
    -- 更新高度统计
    if currentPosition.Y > testState.testResults.maxHeight then
        testState.testResults.maxHeight = currentPosition.Y
    end
    if currentPosition.Y < testState.testResults.minHeight then
        testState.testResults.minHeight = currentPosition.Y
    end
    
    -- 计算高度变化
    testState.testResults.heightVariation = testState.testResults.maxHeight - testState.testResults.minHeight
    
    -- 检测飞行
    if testState.testResults.beforeEquip then
        local heightChange = currentPosition.Y - testState.testResults.beforeEquip.Y
        if math.abs(heightChange) > TEST_CONFIG.flyingThreshold then
            if not testState.testResults.flyingDetected then
                testState.testResults.flyingDetected = true
                print("🚁 检测到飞行! 高度变化: " .. string.format("%.3f", heightChange) .. " 单位")
            end
        end
    end
    
    -- 记录位置
    table.insert(testState.characterPositions, {
        time = currentTime - testState.startTime,
        position = currentPosition,
        height = currentPosition.Y
    })
    
    testState.lastCharacterPosition = currentPosition
end

-- 停止测试
function FlyingFixTest:StopTest()
    if not testState.isMonitoring then
        return
    end
    
    print("🛑 停止测试")
    testState.isMonitoring = false
    
    -- 断开连接
    if testState.monitorConnection then
        testState.monitorConnection:Disconnect()
        testState.monitorConnection = nil
    end
    
    -- 生成测试报告
    self:GenerateTestReport()
end

-- 生成测试报告
function FlyingFixTest:GenerateTestReport()
    print("\n" .. "=" * 60)
    print("🔧 武器飞行问题修复验证测试报告")
    print("=" * 60)
    
    local testDuration = tick() - testState.startTime
    print("🕐 测试时长: " .. string.format("%.2f", testDuration) .. " 秒")
    print("📍 位置记录数: " .. #testState.characterPositions)
    print("")
    
    -- 装备前后位置分析
    if testState.testResults.beforeEquip and testState.testResults.afterEquip then
        local equipMovement = (testState.testResults.afterEquip - testState.testResults.beforeEquip).Magnitude
        print("📏 装备前后位移分析:")
        print("  装备前位置: " .. tostring(testState.testResults.beforeEquip))
        print("  装备后位置: " .. tostring(testState.testResults.afterEquip))
        print("  位移距离: " .. string.format("%.3f", equipMovement) .. " 单位")
        print("  飞行阈值: " .. TEST_CONFIG.flyingThreshold .. " 单位")
        
        if equipMovement > TEST_CONFIG.flyingThreshold then
            print("  结果: ❌ 装备时发生异常移动")
        else
            print("  结果: ✅ 装备过程正常")
        end
        print("")
    end
    
    -- 高度稳定性分析
    print("📊 高度稳定性分析:")
    print("  最大高度: " .. string.format("%.3f", testState.testResults.maxHeight))
    print("  最小高度: " .. string.format("%.3f", testState.testResults.minHeight))
    print("  高度变化: " .. string.format("%.3f", testState.testResults.heightVariation) .. " 单位")
    print("  稳定性阈值: " .. TEST_CONFIG.stabilityThreshold .. " 单位")
    
    if testState.testResults.heightVariation > TEST_CONFIG.stabilityThreshold then
        print("  结果: ⚠️ 高度变化较大")
    else
        print("  结果: ✅ 高度稳定")
    end
    print("")
    
    -- 飞行检测结果
    if testState.testResults.flyingDetected then
        print("🚁 飞行检测结果: ❌ 检测到飞行问题")
    else
        print("🚁 飞行检测结果: ✅ 未检测到飞行问题")
    end
    print("")
    
    -- 武器跟随系统状态
    print("📷 武器跟随系统状态:")
    local isFollowActive = CameraControlService:IsWeaponFollowActive()
    local isFirstPerson = CameraControlService:IsFirstPerson()
    print("  武器跟随激活: " .. tostring(isFollowActive))
    print("  第一人称视角: " .. tostring(isFirstPerson))
    print("")
    
    -- 总体评估
    print("🎯 修复效果评估:")
    local fixSuccessful = not testState.testResults.flyingDetected and 
                         testState.testResults.heightVariation <= TEST_CONFIG.stabilityThreshold
    
    if fixSuccessful then
        print("✅ 修复成功! BodyPosition方案有效解决了飞行问题")
        print("💡 系统状态:")
        print("  - 角色不会飞起来")
        print("  - 武器跟随功能正常")
        print("  - 角色移动稳定")
        print("  - 物理系统不受影响")
    else
        print("❌ 仍存在问题，需要进一步调试")
        if testState.testResults.flyingDetected then
            print("💡 建议检查BodyPosition的MaxForce和阻尼设置")
        end
        if testState.testResults.heightVariation > TEST_CONFIG.stabilityThreshold then
            print("💡 建议调整BodyPosition的P和D参数")
        end
    end
    
    print("=" * 60)
    print("📋 测试完成")
    
    if fixSuccessful then
        print("🎉 恭喜！武器飞行问题已成功修复！")
    end
end

-- 自动初始化
FlyingFixTest:Initialize()

return FlyingFixTest
