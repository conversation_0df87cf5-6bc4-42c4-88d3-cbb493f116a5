--[[
武器旋转错误修复验证测试脚本
验证BodyOrientation方案是否解决了BodyAngularVelocity的D属性错误
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local RotationFixTest = {}

-- 测试状态
local testState = {
    isMonitoring = false,
    startTime = 0,
    rotationData = {},
    errorCount = 0,
    successfulRotations = 0,
    testResults = {
        initializationSuccess = false,
        rotationWorking = false,
        noErrors = true,
        bodyOrientationFound = false
    },
    monitorConnection = nil
}

-- 测试配置
local TEST_CONFIG = {
    monitorDuration = 8, -- 监控持续时间（秒）
    rotationTestAngles = {
        {pitch = 30, name = "向上30度"},
        {pitch = -30, name = "向下30度"},
        {pitch = 45, name = "向上45度"},
        {pitch = -45, name = "向下45度"}
    },
    testWeapons = {"HyperlaserGun", "MP5K"}
}

-- 初始化测试系统
function RotationFixTest:Initialize()
    print("🔄 武器旋转错误修复验证测试系统已加载")
    print("📋 测试目标:")
    print("  1. 验证BodyOrientation替代BodyAngularVelocity")
    print("  2. 确认不再出现D属性错误")
    print("  3. 检查武器旋转跟随功能正常")
    print("  4. 验证错误修复的有效性")
    print("")
    print("🔑 快捷键:")
    print("  F3 - 开始旋转修复验证测试")
    
    self:SetupHotkeys()
    
    -- 初始化相关服务
    WeaponClient:Initialize()
    CameraControlService:Initialize()
end

-- 设置快捷键
function RotationFixTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F3 then
            self:StartRotationFixTest()
        end
    end)
end

-- 开始旋转修复验证测试
function RotationFixTest:StartRotationFixTest()
    if testState.isMonitoring then
        print("⚠️ 测试已在进行中")
        return
    end
    
    print("🚀 开始武器旋转错误修复验证测试")
    print("=" * 50)
    
    -- 重置测试状态
    testState.isMonitoring = true
    testState.startTime = tick()
    testState.rotationData = {}
    testState.errorCount = 0
    testState.successfulRotations = 0
    testState.testResults = {
        initializationSuccess = false,
        rotationWorking = false,
        noErrors = true,
        bodyOrientationFound = false
    }
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 开始监控
    testState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorWeaponRotation()
    end)
    
    -- 执行测试序列
    task.spawn(function()
        self:RunTestSequence()
    end)
    
    print("📊 监控已启动，将持续 " .. TEST_CONFIG.monitorDuration .. " 秒")
end

-- 运行测试序列
function RotationFixTest:RunTestSequence()
    -- 步骤1：装备武器
    print("\n📋 步骤1：装备测试武器")
    local equipSuccess = self:EquipTestWeapon()
    
    if not equipSuccess then
        print("❌ 武器装备失败，终止测试")
        self:StopTest()
        return
    end
    
    task.wait(2) -- 等待武器跟随系统初始化
    
    -- 检查初始化是否成功
    testState.testResults.initializationSuccess = CameraControlService:IsWeaponFollowActive()
    print("🔧 武器跟随初始化: " .. (testState.testResults.initializationSuccess and "✅ 成功" or "❌ 失败"))
    
    if testState.testResults.initializationSuccess then
        -- 步骤2：检查BodyOrientation
        print("\n📋 步骤2：检查BodyOrientation对象")
        self:CheckBodyOrientation()
        
        -- 步骤3：测试旋转功能
        print("\n📋 步骤3：测试武器旋转跟随")
        self:TestWeaponRotation()
    end
    
    -- 等待监控完成
    task.wait(TEST_CONFIG.monitorDuration - 4)
    self:StopTest()
end

-- 装备测试武器
function RotationFixTest:EquipTestWeapon()
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    -- 查找测试武器
    for _, weaponName in ipairs(TEST_CONFIG.testWeapons) do
        local weapon = backpack:FindFirstChild(weaponName)
        if weapon then
            print("🔧 装备测试武器: " .. weaponName)
            weapon.Parent = player.Character
            return true
        end
    end
    
    print("❌ 背包中没有可用的测试武器")
    return false
end

-- 检查BodyOrientation对象
function RotationFixTest:CheckBodyOrientation()
    local player = Players.LocalPlayer
    if not player.Character then
        return
    end
    
    -- 查找装备的武器
    for _, child in pairs(player.Character:GetChildren()) do
        if child:IsA("Tool") then
            local handle = child:FindFirstChild("Handle")
            if handle then
                -- 检查BodyOrientation
                local bodyOrientation = handle:FindFirstChild("BodyOrientation")
                if bodyOrientation then
                    testState.testResults.bodyOrientationFound = true
                    print("✅ 找到BodyOrientation对象")
                    print("  MaxTorque: " .. tostring(bodyOrientation.MaxTorque))
                    print("  P: " .. tostring(bodyOrientation.P))
                    print("  D: " .. tostring(bodyOrientation.D))
                else
                    print("❌ 未找到BodyOrientation对象")
                end
                
                -- 检查是否还有BodyAngularVelocity（应该没有）
                local bodyAngularVelocity = handle:FindFirstChild("BodyAngularVelocity")
                if bodyAngularVelocity then
                    print("⚠️ 仍然存在BodyAngularVelocity对象（应该已被移除）")
                else
                    print("✅ 确认BodyAngularVelocity已被移除")
                end
                break
            end
        end
    end
end

-- 测试武器旋转
function RotationFixTest:TestWeaponRotation()
    local camera = workspace.CurrentCamera
    if not camera then
        print("❌ 相机不存在")
        return
    end
    
    local initialCFrame = camera.CFrame
    
    -- 测试各种角度
    for i, angleTest in ipairs(TEST_CONFIG.rotationTestAngles) do
        print("  测试角度 " .. i .. ": " .. angleTest.name)
        
        -- 应用旋转
        local pitchRad = math.rad(angleTest.pitch)
        local targetCFrame = initialCFrame * CFrame.Angles(pitchRad, 0, 0)
        camera.CFrame = targetCFrame
        
        -- 等待武器跟随
        task.wait(1)
        
        -- 检查跟随是否仍然激活
        local followActive = CameraControlService:IsWeaponFollowActive()
        if followActive then
            testState.successfulRotations = testState.successfulRotations + 1
            print("    结果: ✅ 成功")
        else
            print("    结果: ❌ 失败")
        end
        
        -- 记录旋转数据
        table.insert(testState.rotationData, {
            angle = angleTest,
            success = followActive,
            time = tick() - testState.startTime
        })
    end
    
    -- 恢复初始相机角度
    camera.CFrame = initialCFrame
    
    testState.testResults.rotationWorking = testState.successfulRotations > 0
    print("  旋转测试完成: " .. testState.successfulRotations .. "/" .. #TEST_CONFIG.rotationTestAngles .. " 成功")
end

-- 监控武器旋转
function RotationFixTest:MonitorWeaponRotation()
    -- 这里可以添加实时监控逻辑
    -- 目前主要用于保持测试运行
end

-- 停止测试
function RotationFixTest:StopTest()
    if not testState.isMonitoring then
        return
    end
    
    print("🛑 停止测试")
    testState.isMonitoring = false
    
    -- 断开连接
    if testState.monitorConnection then
        testState.monitorConnection:Disconnect()
        testState.monitorConnection = nil
    end
    
    -- 生成测试报告
    self:GenerateTestReport()
end

-- 生成测试报告
function RotationFixTest:GenerateTestReport()
    print("\n" .. "=" * 60)
    print("🔄 武器旋转错误修复验证测试报告")
    print("=" * 60)
    
    local testDuration = tick() - testState.startTime
    print("🕐 测试时长: " .. string.format("%.2f", testDuration) .. " 秒")
    print("🔄 旋转测试数: " .. #testState.rotationData)
    print("✅ 成功旋转数: " .. testState.successfulRotations)
    print("❌ 错误计数: " .. testState.errorCount)
    print("")
    
    -- 初始化测试结果
    print("🔧 初始化测试结果:")
    print("  武器跟随启动: " .. (testState.testResults.initializationSuccess and "✅ 成功" or "❌ 失败"))
    print("  BodyOrientation创建: " .. (testState.testResults.bodyOrientationFound and "✅ 成功" or "❌ 失败"))
    print("")
    
    -- 旋转测试结果
    print("🔄 旋转测试结果:")
    local rotationSuccessRate = #testState.rotationData > 0 and (testState.successfulRotations / #testState.rotationData * 100) or 0
    print("  成功率: " .. string.format("%.1f", rotationSuccessRate) .. "%")
    print("  旋转功能: " .. (testState.testResults.rotationWorking and "✅ 正常" or "❌ 异常"))
    print("")
    
    -- 错误检测结果
    testState.testResults.noErrors = testState.errorCount == 0
    print("🚨 错误检测结果:")
    print("  D属性错误: " .. (testState.testResults.noErrors and "✅ 已修复" or "❌ 仍存在"))
    print("  错误计数: " .. testState.errorCount)
    print("")
    
    -- 武器跟随系统状态
    print("📷 武器跟随系统状态:")
    local isFollowActive = CameraControlService:IsWeaponFollowActive()
    local isFirstPerson = CameraControlService:IsFirstPerson()
    print("  武器跟随激活: " .. tostring(isFollowActive))
    print("  第一人称视角: " .. tostring(isFirstPerson))
    print("")
    
    -- 总体评估
    print("🎯 修复效果评估:")
    local fixSuccessful = testState.testResults.initializationSuccess and 
                         testState.testResults.bodyOrientationFound and 
                         testState.testResults.rotationWorking and 
                         testState.testResults.noErrors
    
    if fixSuccessful then
        print("✅ 修复成功! BodyOrientation方案有效解决了D属性错误")
        print("💡 系统状态:")
        print("  - BodyAngularVelocity已被BodyOrientation替代")
        print("  - D属性错误已修复")
        print("  - 武器旋转跟随功能正常")
        print("  - 系统运行稳定")
    else
        print("❌ 仍存在问题，需要进一步调试")
        if not testState.testResults.initializationSuccess then
            print("💡 建议检查武器跟随初始化逻辑")
        end
        if not testState.testResults.bodyOrientationFound then
            print("💡 建议检查BodyOrientation创建代码")
        end
        if not testState.testResults.rotationWorking then
            print("💡 建议检查旋转控制逻辑")
        end
        if not testState.testResults.noErrors then
            print("💡 建议检查是否还有其他属性错误")
        end
    end
    
    print("=" * 60)
    print("📋 测试完成")
    
    if fixSuccessful then
        print("🎉 恭喜！武器旋转D属性错误已成功修复！")
    end
end

-- 自动初始化
RotationFixTest:Initialize()

return RotationFixTest
