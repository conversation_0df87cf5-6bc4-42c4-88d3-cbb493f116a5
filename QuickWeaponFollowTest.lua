--[[
快速武器跟随功能测试脚本
用于快速验证远程武器跟随鼠标转动功能是否正常工作
]]

local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local QuickTest = {}

-- 快速测试
function QuickTest:RunQuickTest()
    print("🚀 开始快速武器跟随测试")
    print("=" * 40)
    
    -- 初始化服务
    print("📋 1. 初始化服务...")
    WeaponClient:Initialize()
    CameraControlService:Initialize()
    task.wait(1)
    
    -- 设置第一人称视角
    print("📋 2. 设置第一人称视角...")
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 尝试装备远程武器
    print("📋 3. 尝试装备远程武器...")
    local weaponEquipped = self:EquipRemoteWeapon()
    
    if not weaponEquipped then
        print("❌ 无法装备远程武器，测试终止")
        return false
    end
    
    task.wait(2)
    
    -- 检查武器跟随是否激活
    print("📋 4. 检查武器跟随状态...")
    local followActive = CameraControlService:IsWeaponFollowActive()
    print("   武器跟随状态: " .. (followActive and "✅ 激活" or "❌ 未激活"))
    
    if followActive then
        print("📋 5. 测试基本转动...")
        self:TestBasicRotation()
        
        print("📋 6. 测试跟随停止...")
        self:TestFollowStop()
    end
    
    print("=" * 40)
    print("🎉 快速测试完成")
    
    return followActive
end

-- 装备远程武器
function QuickTest:EquipRemoteWeapon()
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    -- 查找远程武器
    local remoteWeapons = {"HyperlaserGun", "MP5K"}
    
    for _, weaponName in ipairs(remoteWeapons) do
        local weapon = backpack:FindFirstChild(weaponName)
        if weapon then
            weapon.Parent = player.Character
            print("   ✅ 已装备远程武器: " .. weaponName)
            return true
        end
    end
    
    -- 如果没找到预设武器，查找任何Tool
    print("   💡 查找可用武器:")
    for _, child in pairs(backpack:GetChildren()) do
        if child:IsA("Tool") then
            print("     - " .. child.Name)
            -- 装备第一个找到的工具进行测试
            child.Parent = player.Character
            print("   ✅ 已装备测试武器: " .. child.Name)
            return true
        end
    end
    
    print("❌ 背包中没有可用武器")
    return false
end

-- 测试基本转动
function QuickTest:TestBasicRotation()
    local camera = workspace.CurrentCamera
    if not camera then
        print("❌ 相机不存在")
        return
    end
    
    local initialCFrame = camera.CFrame
    
    -- 测试向上转动
    print("   🔄 测试向上转动30度...")
    camera.CFrame = initialCFrame * CFrame.Angles(math.rad(-30), 0, 0)
    task.wait(1)
    
    -- 测试向下转动
    print("   🔄 测试向下转动30度...")
    camera.CFrame = initialCFrame * CFrame.Angles(math.rad(30), 0, 0)
    task.wait(1)
    
    -- 测试左右转动
    print("   🔄 测试左右转动45度...")
    camera.CFrame = initialCFrame * CFrame.Angles(0, math.rad(45), 0)
    task.wait(1)
    camera.CFrame = initialCFrame * CFrame.Angles(0, math.rad(-45), 0)
    task.wait(1)
    
    -- 恢复初始角度
    camera.CFrame = initialCFrame
    print("   ✅ 基本转动测试完成")
end

-- 测试跟随停止
function QuickTest:TestFollowStop()
    local player = Players.LocalPlayer
    
    -- 卸载武器
    if player.Character then
        for _, child in pairs(player.Character:GetChildren()) do
            if child:IsA("Tool") then
                child.Parent = player.Backpack
                print("   🛑 已卸载武器: " .. child.Name)
                break
            end
        end
    end
    
    task.wait(1)
    
    -- 检查跟随是否停止
    local followActive = CameraControlService:IsWeaponFollowActive()
    print("   跟随停止状态: " .. (not followActive and "✅ 成功停止" or "❌ 仍在运行"))
end

-- 设置快捷键
function QuickTest:SetupHotkeys()
    print("🔑 快速测试快捷键:")
    print("  F8 - 运行快速测试")
    
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F8 then
            self:RunQuickTest()
        end
    end)
end

-- 初始化
function QuickTest:Initialize()
    print("⚡ 快速武器跟随测试系统已加载")
    self:SetupHotkeys()
    print("💡 按F8键运行快速测试")
end

-- 自动初始化
QuickTest:Initialize()

return QuickTest
