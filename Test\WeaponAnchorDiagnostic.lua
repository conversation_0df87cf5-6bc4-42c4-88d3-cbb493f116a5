--[[
武器锚固问题诊断脚本
专门诊断远程武器Handle被锚定导致角色无法移动的问题
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local AnchorDiagnostic = {}

-- 诊断状态
local diagnosticState = {
    isMonitoring = false,
    startTime = 0,
    characterMovementData = {},
    handleStates = {},
    monitorConnection = nil,
    lastCharacterPosition = nil,
    movementBlocked = false,
    anchorEvents = {}
}

-- 诊断配置
local DIAGNOSTIC_CONFIG = {
    monitorDuration = 10, -- 监控持续时间（秒）
    positionCheckInterval = 0.1, -- 位置检查间隔（秒）
    movementThreshold = 0.1, -- 移动检测阈值
    testWeapons = {"HyperlaserGun", "MP5K"}
}

-- 初始化诊断系统
function AnchorDiagnostic:Initialize()
    print("🔍 武器锚固问题诊断系统已加载")
    print("📋 诊断功能:")
    print("  1. 监控Handle锚定状态变化")
    print("  2. 检测角色移动能力")
    print("  3. 分析锚定对角色移动的影响")
    print("  4. 提供修复建议")
    print("")
    print("🔑 快捷键:")
    print("  F6 - 开始锚固问题诊断")
    print("  Ctrl+F6 - 停止诊断")
    
    self:SetupHotkeys()
end

-- 设置快捷键
function AnchorDiagnostic:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F6 then
            if UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
                self:StopDiagnostic()
            else
                self:StartDiagnostic()
            end
        end
    end)
end

-- 开始诊断
function AnchorDiagnostic:StartDiagnostic()
    if diagnosticState.isMonitoring then
        print("⚠️ 诊断已在进行中")
        return
    end
    
    print("🚀 开始武器锚固问题诊断")
    print("=" * 50)
    
    -- 重置状态
    diagnosticState.isMonitoring = true
    diagnosticState.startTime = tick()
    diagnosticState.characterMovementData = {}
    diagnosticState.handleStates = {}
    diagnosticState.movementBlocked = false
    diagnosticState.anchorEvents = {}
    
    -- 记录初始状态
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        diagnosticState.lastCharacterPosition = player.Character.HumanoidRootPart.Position
        self:RecordCharacterState("INITIAL")
    end
    
    -- 开始监控循环
    diagnosticState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorCharacterMovement()
        self:MonitorHandleStates()
    end)
    
    -- 自动停止诊断
    task.spawn(function()
        task.wait(DIAGNOSTIC_CONFIG.monitorDuration)
        if diagnosticState.isMonitoring then
            self:StopDiagnostic()
        end
    end)
    
    print("📊 诊断已启动，将持续 " .. DIAGNOSTIC_CONFIG.monitorDuration .. " 秒")
    print("💡 现在装备一个远程武器并尝试移动角色...")
end

-- 停止诊断
function AnchorDiagnostic:StopDiagnostic()
    if not diagnosticState.isMonitoring then
        print("⚠️ 诊断未在进行中")
        return
    end
    
    print("🛑 停止诊断")
    diagnosticState.isMonitoring = false
    
    -- 断开连接
    if diagnosticState.monitorConnection then
        diagnosticState.monitorConnection:Disconnect()
        diagnosticState.monitorConnection = nil
    end
    
    -- 生成诊断报告
    self:GenerateDiagnosticReport()
end

-- 监控角色移动
function AnchorDiagnostic:MonitorCharacterMovement()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local currentPosition = player.Character.HumanoidRootPart.Position
    local currentTime = tick()
    
    if diagnosticState.lastCharacterPosition then
        local movement = (currentPosition - diagnosticState.lastCharacterPosition).Magnitude
        
        -- 检查是否有输入但没有移动（可能被锚定阻止）
        local humanoid = player.Character:FindFirstChild("Humanoid")
        if humanoid then
            local moveVector = humanoid.MoveDirection
            local isMoving = moveVector.Magnitude > 0
            
            if isMoving and movement < DIAGNOSTIC_CONFIG.movementThreshold then
                if not diagnosticState.movementBlocked then
                    diagnosticState.movementBlocked = true
                    self:RecordCharacterState("MOVEMENT_BLOCKED")
                    print("🚨 检测到移动被阻止! 有输入但角色无法移动")
                end
            elseif movement > DIAGNOSTIC_CONFIG.movementThreshold then
                if diagnosticState.movementBlocked then
                    diagnosticState.movementBlocked = false
                    self:RecordCharacterState("MOVEMENT_RESTORED")
                    print("✅ 角色移动已恢复")
                end
            end
        end
    end
    
    diagnosticState.lastCharacterPosition = currentPosition
end

-- 监控Handle状态
function AnchorDiagnostic:MonitorHandleStates()
    local player = Players.LocalPlayer
    if not player.Character then
        return
    end
    
    -- 检查装备的武器
    for _, child in pairs(player.Character:GetChildren()) do
        if child:IsA("Tool") then
            local handle = child:FindFirstChild("Handle")
            if handle then
                self:CheckHandleState(handle, child.Name)
            end
        end
    end
end

-- 检查Handle状态
function AnchorDiagnostic:CheckHandleState(handle, weaponName)
    local currentTime = tick() - diagnosticState.startTime
    local handleId = weaponName .. "_Handle"
    
    -- 记录当前状态
    local currentState = {
        time = currentTime,
        weaponName = weaponName,
        anchored = handle.Anchored,
        canCollide = handle.CanCollide,
        canQuery = handle.CanQuery,
        massless = handle.Massless,
        position = handle.Position,
        cframe = handle.CFrame
    }
    
    -- 检查状态变化
    local lastState = diagnosticState.handleStates[handleId]
    if not lastState or lastState.anchored ~= currentState.anchored then
        table.insert(diagnosticState.anchorEvents, {
            time = currentTime,
            weaponName = weaponName,
            event = currentState.anchored and "ANCHORED" or "UNANCHORED",
            previousState = lastState,
            currentState = currentState
        })
        
        print("🔧 Handle状态变化 (" .. weaponName .. "): " .. 
              (currentState.anchored and "已锚定" or "已解锚"))
    end
    
    diagnosticState.handleStates[handleId] = currentState
end

-- 记录角色状态
function AnchorDiagnostic:RecordCharacterState(eventType)
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    table.insert(diagnosticState.characterMovementData, {
        time = tick() - diagnosticState.startTime,
        eventType = eventType,
        position = player.Character.HumanoidRootPart.Position,
        canMove = not diagnosticState.movementBlocked
    })
end

-- 生成诊断报告
function AnchorDiagnostic:GenerateDiagnosticReport()
    print("\n" .. "=" * 60)
    print("📊 武器锚固问题诊断报告")
    print("=" * 60)
    
    local diagnosticDuration = tick() - diagnosticState.startTime
    print("🕐 诊断时长: " .. string.format("%.2f", diagnosticDuration) .. " 秒")
    print("📍 角色状态记录数: " .. #diagnosticState.characterMovementData)
    print("🔧 锚定事件数: " .. #diagnosticState.anchorEvents)
    print("")
    
    -- 移动阻止分析
    if diagnosticState.movementBlocked then
        print("🚨 移动阻止检测结果: 角色移动被阻止!")
        print("💡 可能原因: 武器Handle被锚定影响角色移动")
    else
        print("✅ 移动检测结果: 角色可以正常移动")
    end
    print("")
    
    -- 锚定事件分析
    print("🔧 Handle锚定事件时间线:")
    for _, event in ipairs(diagnosticState.anchorEvents) do
        print("  " .. string.format("%.3f", event.time) .. "s - " .. 
              event.weaponName .. ": " .. event.event)
        
        if event.currentState then
            print("    状态: Anchored=" .. tostring(event.currentState.anchored) .. 
                  ", CanCollide=" .. tostring(event.currentState.canCollide))
        end
    end
    print("")
    
    -- 角色状态变化
    print("👤 角色状态变化:")
    for _, record in ipairs(diagnosticState.characterMovementData) do
        print("  " .. string.format("%.3f", record.time) .. "s - " .. 
              record.eventType .. " (可移动: " .. tostring(record.canMove) .. ")")
    end
    print("")
    
    -- 当前武器跟随系统状态
    print("📷 武器跟随系统状态:")
    local isFollowActive = CameraControlService:IsWeaponFollowActive()
    local isFirstPerson = CameraControlService:IsFirstPerson()
    print("  武器跟随激活: " .. tostring(isFollowActive))
    print("  第一人称视角: " .. tostring(isFirstPerson))
    print("")
    
    -- 问题分析和修复建议
    print("🔍 问题分析:")
    if #diagnosticState.anchorEvents > 0 then
        local hasAnchoredEvents = false
        for _, event in ipairs(diagnosticState.anchorEvents) do
            if event.event == "ANCHORED" then
                hasAnchoredEvents = true
                break
            end
        end
        
        if hasAnchoredEvents then
            print("❌ 检测到Handle被锚定")
            print("💡 问题原因:")
            print("  1. 武器跟随系统将Handle锚定了")
            print("  2. 锚定的Handle通过Tool机制影响角色移动")
            print("  3. WeldConstraint + Anchored Handle = 角色无法移动")
            
            print("\n🔧 修复建议:")
            print("  1. 不要锚定武器Handle")
            print("  2. 使用其他方式固定武器位置")
            print("  3. 考虑使用BodyPosition或BodyVelocity")
            print("  4. 或者完全重新设计武器跟随机制")
        else
            print("✅ Handle锚定状态正常")
        end
    else
        print("ℹ️ 未检测到Handle状态变化")
    end
    
    print("=" * 60)
    print("📋 诊断完成")
end

-- 自动初始化
AnchorDiagnostic:Initialize()

return AnchorDiagnostic
