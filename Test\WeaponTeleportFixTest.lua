--[[
武器瞬移问题修复验证测试脚本
专门测试修复后的武器跟随系统是否还会导致角色瞬移
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local TeleportFixTest = {}

-- 测试状态
local testState = {
    isMonitoring = false,
    startTime = 0,
    characterPositions = {},
    weaponEvents = {},
    lastCharacterPosition = nil,
    monitorConnection = nil,
    teleportDetected = false,
    testResults = {
        beforeEquip = nil,
        afterEquip = nil,
        maxMovement = 0,
        totalMovement = 0,
        movementCount = 0
    }
}

-- 测试配置
local TEST_CONFIG = {
    monitorDuration = 5, -- 监控持续时间（秒）
    teleportThreshold = 3.0, -- 瞬移检测阈值（单位距离）
    testWeapons = {"HyperlaserGun", "MP5K"} -- 测试武器列表
}

-- 初始化测试系统
function TeleportFixTest:Initialize()
    print("🔧 武器瞬移修复验证测试系统已加载")
    print("📋 测试目标:")
    print("  1. 验证装备远程武器时不会产生瞬移")
    print("  2. 检查武器跟随系统的稳定性")
    print("  3. 确认角色位置保持稳定")
    print("")
    print("🔑 快捷键:")
    print("  F7 - 开始瞬移修复验证测试")
    
    self:SetupHotkeys()
    
    -- 初始化相关服务
    WeaponClient:Initialize()
    CameraControlService:Initialize()
end

-- 设置快捷键
function TeleportFixTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F7 then
            self:StartTeleportFixTest()
        end
    end)
end

-- 开始瞬移修复验证测试
function TeleportFixTest:StartTeleportFixTest()
    if testState.isMonitoring then
        print("⚠️ 测试已在进行中")
        return
    end
    
    print("🚀 开始武器瞬移修复验证测试")
    print("=" * 50)
    
    -- 重置测试状态
    testState.isMonitoring = true
    testState.startTime = tick()
    testState.characterPositions = {}
    testState.weaponEvents = {}
    testState.teleportDetected = false
    testState.testResults = {
        beforeEquip = nil,
        afterEquip = nil,
        maxMovement = 0,
        totalMovement = 0,
        movementCount = 0
    }
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 记录装备前的位置
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        testState.testResults.beforeEquip = player.Character.HumanoidRootPart.Position
        testState.lastCharacterPosition = testState.testResults.beforeEquip
        print("📍 装备前角色位置: " .. tostring(testState.testResults.beforeEquip))
    end
    
    -- 开始位置监控
    testState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorCharacterPosition()
    end)
    
    -- 装备武器
    task.spawn(function()
        task.wait(1) -- 等待1秒开始监控
        self:EquipTestWeapon()
        
        task.wait(TEST_CONFIG.monitorDuration) -- 监控指定时间
        self:StopTest()
    end)
    
    print("📊 监控已启动，将持续 " .. (TEST_CONFIG.monitorDuration + 1) .. " 秒")
end

-- 装备测试武器
function TeleportFixTest:EquipTestWeapon()
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    -- 查找测试武器
    for _, weaponName in ipairs(TEST_CONFIG.testWeapons) do
        local weapon = backpack:FindFirstChild(weaponName)
        if weapon then
            print("🔧 装备测试武器: " .. weaponName)
            
            -- 记录装备时间
            table.insert(testState.weaponEvents, {
                time = tick() - testState.startTime,
                event = "EQUIPPING",
                weapon = weaponName
            })
            
            -- 装备武器
            weapon.Parent = player.Character
            
            -- 等待一帧后记录装备后位置
            task.wait()
            if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
                testState.testResults.afterEquip = player.Character.HumanoidRootPart.Position
                print("📍 装备后角色位置: " .. tostring(testState.testResults.afterEquip))
                
                -- 计算装备前后的位移
                if testState.testResults.beforeEquip then
                    local equipMovement = (testState.testResults.afterEquip - testState.testResults.beforeEquip).Magnitude
                    print("📏 装备前后位移: " .. string.format("%.3f", equipMovement) .. " 单位")
                    
                    if equipMovement > TEST_CONFIG.teleportThreshold then
                        testState.teleportDetected = true
                        print("🚨 检测到装备时瞬移! 位移: " .. string.format("%.3f", equipMovement) .. " 单位")
                    else
                        print("✅ 装备过程正常，无瞬移")
                    end
                end
            end
            
            table.insert(testState.weaponEvents, {
                time = tick() - testState.startTime,
                event = "EQUIPPED",
                weapon = weaponName
            })
            
            return true
        end
    end
    
    print("❌ 背包中没有可用的测试武器")
    print("💡 可用武器:")
    for _, child in pairs(backpack:GetChildren()) do
        if child:IsA("Tool") then
            print("  - " .. child.Name)
        end
    end
    return false
end

-- 监控角色位置
function TeleportFixTest:MonitorCharacterPosition()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local currentPosition = player.Character.HumanoidRootPart.Position
    local currentTime = tick()
    
    if testState.lastCharacterPosition then
        local movement = (currentPosition - testState.lastCharacterPosition).Magnitude
        
        -- 记录移动数据
        testState.testResults.totalMovement = testState.testResults.totalMovement + movement
        testState.testResults.movementCount = testState.testResults.movementCount + 1
        
        if movement > testState.testResults.maxMovement then
            testState.testResults.maxMovement = movement
        end
        
        -- 检测瞬移
        if movement > TEST_CONFIG.teleportThreshold then
            testState.teleportDetected = true
            print("🚨 检测到瞬移! 移动距离: " .. string.format("%.3f", movement) .. " 单位")
            
            table.insert(testState.characterPositions, {
                time = currentTime - testState.startTime,
                position = currentPosition,
                movement = movement,
                type = "TELEPORT"
            })
        elseif movement > 0.1 then -- 记录较大的移动
            table.insert(testState.characterPositions, {
                time = currentTime - testState.startTime,
                position = currentPosition,
                movement = movement,
                type = "NORMAL"
            })
        end
    end
    
    testState.lastCharacterPosition = currentPosition
end

-- 停止测试
function TeleportFixTest:StopTest()
    if not testState.isMonitoring then
        return
    end
    
    print("🛑 停止测试")
    testState.isMonitoring = false
    
    -- 断开连接
    if testState.monitorConnection then
        testState.monitorConnection:Disconnect()
        testState.monitorConnection = nil
    end
    
    -- 生成测试报告
    self:GenerateTestReport()
end

-- 生成测试报告
function TeleportFixTest:GenerateTestReport()
    print("\n" .. "=" * 60)
    print("📊 武器瞬移修复验证测试报告")
    print("=" * 60)
    
    local testDuration = tick() - testState.startTime
    print("🕐 测试时长: " .. string.format("%.2f", testDuration) .. " 秒")
    print("📍 位置记录数: " .. #testState.characterPositions)
    print("🔧 武器事件数: " .. #testState.weaponEvents)
    print("")
    
    -- 装备前后位置分析
    if testState.testResults.beforeEquip and testState.testResults.afterEquip then
        local equipMovement = (testState.testResults.afterEquip - testState.testResults.beforeEquip).Magnitude
        print("📏 装备前后位移分析:")
        print("  装备前位置: " .. tostring(testState.testResults.beforeEquip))
        print("  装备后位置: " .. tostring(testState.testResults.afterEquip))
        print("  位移距离: " .. string.format("%.3f", equipMovement) .. " 单位")
        print("  瞬移阈值: " .. TEST_CONFIG.teleportThreshold .. " 单位")
        
        if equipMovement > TEST_CONFIG.teleportThreshold then
            print("  结果: ❌ 装备时发生瞬移")
        else
            print("  结果: ✅ 装备过程正常")
        end
        print("")
    end
    
    -- 移动统计
    local avgMovement = testState.testResults.movementCount > 0 and 
                       (testState.testResults.totalMovement / testState.testResults.movementCount) or 0
    
    print("📊 移动统计:")
    print("  最大单次移动: " .. string.format("%.3f", testState.testResults.maxMovement) .. " 单位")
    print("  平均移动距离: " .. string.format("%.3f", avgMovement) .. " 单位")
    print("  总移动距离: " .. string.format("%.3f", testState.testResults.totalMovement) .. " 单位")
    print("  移动次数: " .. testState.testResults.movementCount)
    print("")
    
    -- 瞬移检测结果
    if testState.teleportDetected then
        print("🚨 瞬移检测结果: 检测到瞬间移动!")
        
        -- 显示瞬移事件
        local teleportEvents = {}
        for _, record in ipairs(testState.characterPositions) do
            if record.type == "TELEPORT" then
                table.insert(teleportEvents, record)
            end
        end
        
        print("📊 瞬移事件详情:")
        for i, event in ipairs(teleportEvents) do
            print("  事件 " .. i .. ":")
            print("    时间: " .. string.format("%.3f", event.time) .. "s")
            print("    移动距离: " .. string.format("%.3f", event.movement) .. " 单位")
            print("    位置: " .. tostring(event.position))
        end
    else
        print("✅ 瞬移检测结果: 未检测到瞬间移动")
    end
    
    -- 武器事件时间线
    print("\n🔧 武器事件时间线:")
    for _, event in ipairs(testState.weaponEvents) do
        print("  " .. string.format("%.3f", event.time) .. "s - " .. event.event .. ": " .. event.weapon)
    end
    
    -- 武器跟随系统状态
    print("\n📷 武器跟随系统状态:")
    local isFollowActive = CameraControlService:IsWeaponFollowActive()
    local isFirstPerson = CameraControlService:IsFirstPerson()
    print("  武器跟随激活: " .. tostring(isFollowActive))
    print("  第一人称视角: " .. tostring(isFirstPerson))
    
    -- 总体评估
    print("\n🎯 修复效果评估:")
    if not testState.teleportDetected and testState.testResults.maxMovement < TEST_CONFIG.teleportThreshold then
        print("✅ 修复成功! 武器跟随系统工作正常，无瞬移问题")
        print("💡 系统已稳定，可以正常使用")
    else
        print("❌ 仍存在问题，需要进一步调试")
        if testState.teleportDetected then
            print("💡 建议检查WeldConstraint的创建时机和Handle的物理属性设置")
        end
        if testState.testResults.maxMovement >= TEST_CONFIG.teleportThreshold then
            print("💡 建议降低武器跟随的平滑度或检查位置计算逻辑")
        end
    end
    
    print("=" * 60)
    print("📋 测试完成")
end

-- 自动初始化
TeleportFixTest:Initialize()

return TeleportFixTest
