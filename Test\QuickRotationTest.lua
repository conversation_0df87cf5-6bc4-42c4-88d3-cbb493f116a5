--[[
快速武器旋转修复验证测试
简单验证BodyAngularVelocity是否正确创建且无属性错误
]]

local Players = game:GetService("Players")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local QuickTest = {}

-- 初始化测试系统
function QuickTest:Initialize()
    print("⚡ 快速武器旋转修复验证测试已加载")
    print("🔑 快捷键: F2 - 快速验证修复")
    
    self:SetupHotkeys()
    
    -- 初始化相关服务
    WeaponClient:Initialize()
    CameraControlService:Initialize()
end

-- 设置快捷键
function QuickTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F2 then
            self:RunQuickTest()
        end
    end)
end

-- 运行快速测试
function QuickTest:RunQuickTest()
    print("\n🚀 开始快速武器旋转修复验证")
    print("=" * 40)
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备武器
    local equipSuccess = self:EquipAnyWeapon()
    if not equipSuccess then
        print("❌ 无法装备武器，测试终止")
        return
    end
    
    task.wait(2) -- 等待武器跟随系统初始化
    
    -- 检查武器跟随是否激活
    local followActive = CameraControlService:IsWeaponFollowActive()
    print("🔧 武器跟随状态: " .. (followActive and "✅ 激活" or "❌ 未激活"))
    
    if followActive then
        -- 检查Body对象
        self:CheckBodyObjects()
        
        -- 简单旋转测试
        self:SimpleRotationTest()
    end
    
    print("=" * 40)
    print("📋 快速测试完成")
end

-- 装备任意武器
function QuickTest:EquipAnyWeapon()
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    -- 查找任意武器
    for _, child in pairs(backpack:GetChildren()) do
        if child:IsA("Tool") then
            print("🔧 装备武器: " .. child.Name)
            child.Parent = player.Character
            return true
        end
    end
    
    print("❌ 背包中没有武器")
    return false
end

-- 检查Body对象
function QuickTest:CheckBodyObjects()
    local player = Players.LocalPlayer
    if not player.Character then
        return
    end
    
    -- 查找装备的武器
    for _, child in pairs(player.Character:GetChildren()) do
        if child:IsA("Tool") then
            local handle = child:FindFirstChild("Handle")
            if handle then
                print("\n🔍 检查Body对象:")
                
                -- 检查BodyPosition
                local bodyPosition = handle:FindFirstChild("BodyPosition")
                if bodyPosition then
                    print("✅ BodyPosition: 存在")
                    print("  MaxForce: " .. tostring(bodyPosition.MaxForce))
                    print("  P: " .. tostring(bodyPosition.P))
                    print("  D: " .. tostring(bodyPosition.D))
                else
                    print("❌ BodyPosition: 不存在")
                end
                
                -- 检查BodyAngularVelocity
                local bodyAngularVelocity = handle:FindFirstChild("BodyAngularVelocity")
                if bodyAngularVelocity then
                    print("✅ BodyAngularVelocity: 存在")
                    print("  MaxTorque: " .. tostring(bodyAngularVelocity.MaxTorque))
                    print("  AngularVelocity: " .. tostring(bodyAngularVelocity.AngularVelocity))
                    
                    -- 测试属性访问（确保不会出错）
                    local success, error = pcall(function()
                        local _ = bodyAngularVelocity.MaxTorque
                        local _ = bodyAngularVelocity.AngularVelocity
                    end)
                    
                    if success then
                        print("✅ 属性访问: 正常")
                    else
                        print("❌ 属性访问错误: " .. tostring(error))
                    end
                    
                    -- 测试是否还有P和D属性（应该没有）
                    local hasP = pcall(function() return bodyAngularVelocity.P end)
                    local hasD = pcall(function() return bodyAngularVelocity.D end)
                    
                    if not hasP and not hasD then
                        print("✅ P和D属性: 已正确移除")
                    else
                        print("⚠️ P和D属性: 仍然存在（可能有问题）")
                    end
                else
                    print("❌ BodyAngularVelocity: 不存在")
                end
                
                break
            end
        end
    end
end

-- 简单旋转测试
function QuickTest:SimpleRotationTest()
    print("\n🔄 简单旋转测试:")
    
    local camera = workspace.CurrentCamera
    if not camera then
        print("❌ 相机不存在")
        return
    end
    
    local initialCFrame = camera.CFrame
    
    -- 测试向上旋转
    print("  测试向上旋转30度...")
    local upRotation = initialCFrame * CFrame.Angles(math.rad(30), 0, 0)
    camera.CFrame = upRotation
    task.wait(1)
    
    -- 检查跟随是否仍然激活
    local followActive1 = CameraControlService:IsWeaponFollowActive()
    print("  结果: " .. (followActive1 and "✅ 成功" or "❌ 失败"))
    
    -- 测试向下旋转
    print("  测试向下旋转30度...")
    local downRotation = initialCFrame * CFrame.Angles(math.rad(-30), 0, 0)
    camera.CFrame = downRotation
    task.wait(1)
    
    -- 检查跟随是否仍然激活
    local followActive2 = CameraControlService:IsWeaponFollowActive()
    print("  结果: " .. (followActive2 and "✅ 成功" or "❌ 失败"))
    
    -- 恢复初始角度
    camera.CFrame = initialCFrame
    
    -- 总结
    if followActive1 and followActive2 then
        print("✅ 旋转测试: 通过")
    else
        print("❌ 旋转测试: 失败")
    end
end

-- 自动初始化
QuickTest:Initialize()

return QuickTest
