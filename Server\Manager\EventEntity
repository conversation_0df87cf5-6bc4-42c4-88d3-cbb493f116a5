local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Workspace = game:GetService("Workspace")
local ConfigManager = require(ReplicatedStorage.Scripts.Share.Services.ConfigManager)
local CreatObjAndGUId = require(ReplicatedStorage.Scripts.ItemInteraction.CreatObjAndGUId)
local ObjectTracker = require(ReplicatedStorage.Scripts.ItemInteraction.ObjectTracker) 
local monsterManager = require(ReplicatedStorage.Scripts.Monster.MonsterSpawner)
local EventEntity = {}

local eventConfig
local buildingConfig 
local itemConfig
local monsterConfig  
local equipConfig

-- 初始化时确保必要容器存在
local function ensureContainers()
	-- 确保建筑容器存在
	if not Workspace:FindFirstChild("Buildings") then
		local buildings = Instance.new("Folder")
		buildings.Name = "Buildings"
		buildings.Parent = Workspace
	end
	-- 确保物品容器存在
	if not Workspace:FindFirstChild("Item") then
		local items = Instance.new("Folder")
		items.Name = "Item"
		items.Parent = Workspace
	end
end

local function Init()
	eventConfig = ConfigManager.GetConfig("EventConfig")
	buildingConfig = ConfigManager.GetConfig("BuildingConfig")  
	itemConfig = ConfigManager.GetConfig("ItemConfig")
	monsterConfig = ConfigManager.GetConfig("MonsterConfig")
	equipConfig=ConfigManager.GetConfig("EquipConfig")
	ensureContainers()  -- 初始化容器
end

-- 拆分id（保持不变）
local function ParseEventId(eventId)
	if not eventId or type(eventId) ~= "string" then
		warn("ParseEventId 接收了无效的eventId参数:", eventId)
		return nil
	end

	local result = {}
	local events = string.split(eventId, ";")

	for _, eventStr in ipairs(events) do
		if #eventStr > 0 then
			local parts = string.split(eventStr, "_")
			if #parts < 4 then
				warn("无效的EventId格式:", eventStr)
			else
				local targets = {}
				local targetParts = string.split(parts[4], ",")

				for _, targetPart in ipairs(targetParts) do
					local idAndWeight = string.split(targetPart, "&")
					local targetId = tonumber(idAndWeight[1])
					local weight = idAndWeight[2] and tonumber(idAndWeight[2]) or 1

					if targetId then
						table.insert(targets, {
							id = targetId,
							weight = weight
						})
					else
						warn("无效的目标ID格式:", targetPart)
					end
				end

				if #targets > 0 then
					table.insert(result, {
						chance = tonumber(parts[1]),
						minNum = tonumber(parts[2]),
						maxNum = tonumber(parts[3]),
						targets = targets
					})
				end
			end
		end
	end

	return #result > 0 and result or nil
end

-- 给物品增加标签（保持不变）
local function AddType(obj, config)
	CreatObjAndGUId.ensureSyncId(obj)
	ObjectTracker.registerObject(obj)
	CreatObjAndGUId.ensureRotationValues(obj)

	local id = Instance.new("NumberValue")
	id.Name = "Id"
	id.Parent = obj
	id.Value = config.Id

	local grabbableTag = Instance.new("BoolValue")
	grabbableTag.Name = "Grabbable"
	grabbableTag.Value = false
	grabbableTag.Parent = obj

	local attachTag = Instance.new("BoolValue")
	attachTag.Name = "Attachable"
	attachTag.Value = true
	attachTag.Parent = obj

	local itemType = Instance.new("NumberValue")
	itemType.Name = "ItemType"
	itemType.Value = config.ItemType
	itemType.Parent = obj

	-- 可以出售
	if config.SaleType == 1 then
		local price = Instance.new("NumberValue")
		price.Name = "ObtainNumber"
		price.Value = config.ObtainNumber
		price.Parent = obj
	end

	-- 可以燃烧
	if config.CombustionType == 1 then
		local fuel = Instance.new("NumberValue")
		fuel.Name = "Fuel"
		fuel.Value = config.CombustionValue
		fuel.Parent = obj
	end

	local name = Instance.new("StringValue")
	name.Name = "Name"
	name.Value = config.Name
	name.Parent = obj

	local icon = Instance.new("StringValue")
	icon.Name = "Icon"
	icon.Parent = obj

	local ItemQuality = Instance.new("NumberValue")
	ItemQuality.Name = "ItemQuality"
	ItemQuality.Value = config.ItemQuality or 0
	ItemQuality.Parent = obj
	-- 商品
	if config.PurchaseType == 1 then 
		local price = Instance.new("NumberValue")
		price.Name = "ConsumptionQuantity"
		price.Value = config.ConsumptionQuantity
		price.Parent = obj

		local inTill = Instance.new("BoolValue")
		inTill.Name = "InTill"
		inTill.Value = false
		inTill.Parent = obj
		print("商品: " .. config.Name)
	end
end

-- 添加物品标签（保持不变）
local function AddItems(obj, config)
	
	local Positions = Instance.new("Folder")
	Positions.Name = "Positions"
	local x, y, z = Instance.new("NumberValue"), Instance.new("NumberValue"), Instance.new("NumberValue")
	x.Name, y.Name, z.Name = "X", "Y", "Z"
	x.Parent, y.Parent, z.Parent = Positions, Positions, Positions
	
	
	if config.ItemType == 11 and obj:IsA("Accessory") then
		local handle = obj:FindFirstChild("Handle")
		if handle then
			AddType(handle, config)
			
			
			Positions.Parent = handle
			x.Value, y.Value, z.Value = handle.Position.X, handle.Position.Y, handle.Position.Z
			
			local equipdata = equipConfig:GetDataById(config.Id) 
			if equipdata then  -- 增加判空，避免nil错误
				local AssociateAttributeId = Instance.new("NumberValue")
				AssociateAttributeId.Name = "AssociateAttributeId"
				AssociateAttributeId.Value = equipdata.AssociateAttributeId
				AssociateAttributeId.Parent = handle

				local EquipPart = Instance.new("NumberValue")
				EquipPart.Name = "EquipPart"
				EquipPart.Value = equipdata.EquipmentType
				EquipPart.Parent = handle
			else
				Positions.Parent = obj
				x.Value, y.Value, z.Value = obj.Position.X, obj.Position.Y, obj.Position.Z
				warn("饰品装备数据不存在，ID:", config.Id)
			end
		else
			
			
			warn("Accessory缺少Handle部件:", obj.Name)
		end
	elseif (config.ItemType == 9 or config.ItemType == 10 or config.ItemType == 7) and obj:IsA("Tool") then
		local handle = obj:FindFirstChild("Handle")
		if handle then
			AddType(handle, config)
		else
			warn("Tool缺少Handle部件:", obj.Name)
		end
	else
		AddType(obj, config)
	end
end

-- 概率判断（保持不变）
local function GetRandom(chance)
	chance = math.floor(chance)
	if chance <= 0 then return false end
	if chance >= 10000 then return true end
	return math.random(1, 10000) <= chance
end

-- 根据权重选择ID（保持不变）
local function SelectIdByWeight(targets)
	if not targets or #targets == 0 then
		warn("没有可用的目标ID列表")
		return nil
	end

	local totalWeight = 0
	for _, target in ipairs(targets) do
		totalWeight += (target.weight or 0)
	end

	if totalWeight <= 0 then
		return targets[1].id
	end

	local randomValue = math.random() * totalWeight
	local currentWeight = 0
	for _, target in ipairs(targets) do
		currentWeight += (target.weight or 0)
		if randomValue <= currentWeight then
			return target.id
		end
	end

	return targets[#targets].id
end

-- 获取Part范围内的随机位置（保持不变）
local function getRandomPositionInPart(part)
	if not part or not part:IsA("BasePart") then
		warn("无效的Part，无法生成随机位置")
		return nil
	end

	local size = part.Size
	local cframe = part.CFrame

	local offset = Vector3.new(
		math.random(-size.X/2 * 0.9, size.X/2 * 0.9),
		math.random(-size.Y/2 * 0.9, size.Y/2 * 0.9),
		math.random(-size.Z/2 * 0.9, size.Z/2 * 0.9)
	)

	return cframe:PointToWorldSpace(offset)
end

-- 物品放置函数（保持不变）
local function placeItem(item, containerPart, isTool)
	if not containerPart or not containerPart:IsA("BasePart") then
		warn("无效的容器Part，无法放置物品")
		return
	end

	local position = getRandomPositionInPart(containerPart)
	if not position then return end

	if isTool then
		local handle = item:FindFirstChild("Handle")
		if handle then
			handle.CFrame = CFrame.new(position)
			handle.CanTouch = false
		else
			warn("工具缺少Handle部件:", item.Name)
		end
	elseif item:IsA("Accessory") then
		local handle = item:FindFirstChild("Handle")
		if handle then
			handle.CFrame = CFrame.new(position)
		else
			warn("饰品缺少Handle部件:", item.Name)
		end
	else
		item.CFrame = CFrame.new(position)
	end
end

-- 生成物品的通用函数（保持不变）
local function spawnItemsInPart(itemIdStr, targetPart)
	if not itemIdStr or not targetPart or not targetPart:IsA("BasePart") then
		warn("生成物品失败：无效的ID字符串或目标Part")
		return
	end

	local items = ParseEventId(itemIdStr)
	if not items then
		warn("解析物品ID失败:", itemIdStr)
		return
	end

	for _, itemInfo in ipairs(items) do
		if itemInfo.chance and not GetRandom(itemInfo.chance) then
			continue
		end

		local spawnCount = math.random(itemInfo.minNum, itemInfo.maxNum)
		for _ = 1, spawnCount do
			local targetItemId = SelectIdByWeight(itemInfo.targets)
			if not targetItemId then continue end

			local itemData = itemConfig:GetDataById(targetItemId)
			if not itemData then
				warn("找不到物品数据，ID:", targetItemId)
				continue
			end

			local itemModelName = itemData.ItemModelName
			if not itemModelName then
				warn("物品模型名称为空，ID:", targetItemId)
				continue
			end

			local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemModelName)
			if not itemModel then
				warn("找不到物品模型:", itemModelName)
				continue 
			end

			local item = itemModel:Clone()
			item.Parent = Workspace.Item 

			placeItem(item, targetPart, item:IsA("Tool"))
			AddItems(item, itemData)
		end
	end
end

-- 生成怪物的通用函数
local function spawnMonstersInPart(monsterIdStr, targetPart)
	if not monsterIdStr or not targetPart or not targetPart:IsA("BasePart") then
		warn("生成怪物失败：无效的ID字符串或目标Part")
		return
	end

	local monsters = ParseEventId(monsterIdStr)
	if not monsters then
		warn("解析怪物ID失败:", monsterIdStr)
		return
	end

	for _, monsterInfo in pairs(monsters) do
		if monsterInfo.chance and not GetRandom(monsterInfo.chance) then
			continue
		end

		local spawnCount = math.random(monsterInfo.minNum, monsterInfo.maxNum)
		for _ = 1, spawnCount do
			local targetMonsterId = SelectIdByWeight(monsterInfo.targets)
			if not targetMonsterId then continue end

			local monsterData = monsterConfig:GetDataById(targetMonsterId)
			if not monsterData then
				warn("找不到怪物数据，ID:", targetMonsterId)
				continue
			end

			local spawnPosition = getRandomPositionInPart(targetPart)
			if not spawnPosition then continue end

			local spawner = monsterManager.new()
			spawner:SpawnMonster(monsterData.Id, spawnPosition)
		end
	end
end

-- 主生成函数
function EventEntity:CreatEvent(eventId, pos)
	if not eventId or not pos or typeof(pos) ~= "Vector3" then
		warn("CreatEvent 缺少必要参数或pos无效: eventId=", eventId, "pos=", pos)
		return
	end

	local targetId = eventId
	local eventdata = eventConfig:GetDataById(targetId) 
	if not eventdata then
		warn("找不到事件数据，ID:", targetId)
		return
	end

	local buildingId = eventdata.BuildingGenerationId
	local itemId, itemId2
	local monsterId, monsterId2

	if buildingId then 
		local buildData = buildingConfig:GetDataById(buildingId)  
		if not buildData then
			warn("找不到建筑数据，ID:", buildingId)
			return
		end

		-- 获取物品和怪物ID
		itemId = buildData.BuildingClass1ItemsGeneration
		itemId2 = buildData.BuildingClass2ItemsGeneration
		monsterId = buildData.BuildingMonster1ItemsGeneration
		monsterId2 = buildData.BuildingMonster2ItemsGeneration

		if not buildData.BuildingModel then
			warn("建筑模型名称为空，ID:", buildingId)
			return
		end

		-- 查找建筑模型
		local buildingModel = ReplicatedStorage.Model.BuildingModels:FindFirstChild(buildData.BuildingModel)
		if not buildingModel then
			warn("找不到建筑模型:", buildData.BuildingModel)
			return 
		end

		-- 克隆并生成建筑
		local building = buildingModel:Clone()
		building.Parent = Workspace.Buildings 
		building:PivotTo(CFrame.new(pos))

		-- 判断是否为商店建筑（兼容"Shop"模型或含"Shop"子对象的建筑）
		local isShop = buildData.BuildingModel == "Shop" or building:FindFirstChild("Shop")
		-- 商店分区Part：Store1对应itemId，Store2对应itemId2
		local store1Parts = {}  -- Store1的生成位置
		local store2Parts = {}  -- Store2的生成位置

		if isShop then
			
			local store1Container = building:FindFirstChild("Store1")
			if store1Container then
				for _, child in ipairs(store1Container:GetChildren()) do
					if child:IsA("BasePart") then
						table.insert(store1Parts, child)
					end
				end
				print("Store1有效生成位置数量：", #store1Parts)
			else
				warn("商店缺少Store1子对象，itemId物品无法生成")
			end

		
			local store2Container = building:FindFirstChild("Store2")
			if store2Container then
				for _, child in ipairs(store2Container:GetChildren()) do
					if child:IsA("BasePart") then
						table.insert(store2Parts, child)
					end
				end
				print("Store2有效生成位置数量：", #store2Parts)
			else
				warn("商店缺少Store2子对象，itemId2物品无法生成")
			end
		end

		-- 普通建筑的生成位置（Pos1/Pos2）
		local Pos1 = building:FindFirstChild("Pos1")
		local Pos2 = building:FindFirstChild("Pos2")

		-- 验证普通建筑Part有效性
		if Pos1 and not Pos1:IsA("BasePart") then
			warn("Pos1不是有效的Part:", Pos1.Name)
			Pos1 = nil
		end
		if Pos2 and not Pos2:IsA("BasePart") then
			warn("Pos2不是有效的Part:", Pos2.Name)
			Pos2 = nil
		end

	
		if isShop then
		
			if itemId and #store1Parts > 0 then
				local usedStore1Parts = {}  -- Store1已使用的Part（避免重复）
				local items = ParseEventId(itemId)
				if items then
					for _, itemInfo in ipairs(items) do
						if itemInfo.chance and not GetRandom(itemInfo.chance) then
							continue
						end

						local spawnCount = math.random(itemInfo.minNum, itemInfo.maxNum)
						-- 限制生成数量不超过Store1的可用Part
						spawnCount = math.min(spawnCount, #store1Parts)

						for i = 1, spawnCount do
							-- 筛选Store1中未使用的Part
							local availableParts = {}
							for _, part in ipairs(store1Parts) do
								if not table.find(usedStore1Parts, part) then
									table.insert(availableParts, part)
								end
							end
							if #availableParts == 0 then break end

							-- 随机选择Store1的Part
							local targetPart = availableParts[math.random(1, #availableParts)]
							table.insert(usedStore1Parts, targetPart)

							-- 生成物品
							local targetItemId = SelectIdByWeight(itemInfo.targets)
							if not targetItemId then continue end

							local itemData = itemConfig:GetDataById(targetItemId)
							if not itemData then
								warn("Store1物品数据不存在，ID:", targetItemId)
								continue
							end

							local itemModelName = itemData.ItemModelName
							if not itemModelName then
								warn("Store1物品模型名称为空，ID:", targetItemId)
								continue
							end

							local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemModelName)
							if not itemModel then
								warn("找不到Store1物品模型:", itemModelName)
								continue
							end

							local item = itemModel:Clone()
							placeItem(item, targetPart, item:IsA("Tool"))
							AddItems(item, itemData)
							item.Parent = Workspace.Item
							
							local ConsumptionQuantity=Instance.new("NumberValue")
							ConsumptionQuantity.Name="ConsumptionQuantity"
							ConsumptionQuantity.Value=itemData.ConsumptionQuantity
							
							local intill=Instance.new("BoolValue")
							intill.Name="InTill"
							intill.Value=false
							-- 记录位置（保留原有逻辑，区分Tool和普通物品）
							if item:IsA("Tool") or item:IsA("Accessory") then
								local handle=item:FindFirstChild("Handle")
								ConsumptionQuantity.Parent=handle
								intill.Parent=handle
							else
								
							ConsumptionQuantity.Parent=item
							intill.Parent=item
							end
							
							
						end
							
					end
				end
			elseif itemId then
				warn("Store1容器不存在，itemId物品无法生成")
			end

			
			if itemId2 and #store2Parts > 0 then
				local usedStore2Parts = {}  -- Store2已使用的Part
				local items = ParseEventId(itemId2)
				if items then
					for _, itemInfo in ipairs(items) do
						if itemInfo.chance and not GetRandom(itemInfo.chance) then
							continue
						end

						local spawnCount = math.random(itemInfo.minNum, itemInfo.maxNum)
						spawnCount = math.min(spawnCount, #store2Parts)  -- 限制数量

						for i = 1, spawnCount do
							-- 筛选Store2中未使用的Part
							local availableParts = {}
							for _, part in ipairs(store2Parts) do
								if not table.find(usedStore2Parts, part) then
									table.insert(availableParts, part)
								end
							end
							if #availableParts == 0 then break end

							-- 随机选择Store2的Part
							local targetPart = availableParts[math.random(1, #availableParts)]
							table.insert(usedStore2Parts, targetPart)

							-- 生成物品
							local targetItemId = SelectIdByWeight(itemInfo.targets)
							if not targetItemId then continue end

							local itemData = itemConfig:GetDataById(targetItemId)
							if not itemData then
								warn("Store2物品数据不存在，ID:", targetItemId)
								continue
							end

							local itemModelName = itemData.ItemModelName
							if not itemModelName then
								warn("Store2物品模型名称为空，ID:", targetItemId)
								continue
							end

							local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemModelName)
							if not itemModel then
								warn("找不到Store2物品模型:", itemModelName)
								continue
							end

							local item = itemModel:Clone()
							placeItem(item, targetPart, item:IsA("Tool"))
							AddItems(item, itemData)
							item.Parent = Workspace.Item

							-- 记录位置
							
						end
					end
				end
			elseif itemId2 then
				warn("Store2容器不存在，itemId2物品无法生成")
			end
		else
			-- 非商店建筑：使用Pos1和Pos2生成物品
			if itemId and Pos1 then
				spawnItemsInPart(itemId, Pos1)  
			end
			if itemId2 and Pos2 then
				spawnItemsInPart(itemId2, Pos2) 
			end
		end

		-- 生成怪物（注释保留，按需启用）
		 if monsterId and Pos1 then
		    spawnMonstersInPart(monsterId, Pos1) 
	 	end
		 if monsterId2 and Pos2 then
			     spawnMonstersInPart(monsterId2, Pos2)  
		 end
		end

	-- 无建筑时的物品生成（保持不变）
	if itemId and not buildingId then 
		local items = ParseEventId(itemId)
		if items then
			for _, itemInfo in ipairs(items) do
				for _ = 1, math.random(itemInfo.minNum, itemInfo.maxNum) do
					local targetItemId = SelectIdByWeight(itemInfo.targets)
					local itemData = itemConfig:GetDataById(targetItemId)
					if not itemData then continue end

					local itemModelName = itemData.ItemModelName
					if not itemModelName then continue end

					local itemModel = ReplicatedStorage.Model.Item:FindFirstChild(itemModelName)
					if not itemModel then continue end

					local item = itemModel:Clone()
					item.Parent = Workspace.Item

					-- 使用临时Part放置
					local tempPart = Instance.new("Part")
					tempPart.Parent = Workspace:FindFirstChild("TempParts") or Workspace  -- 兼容无TempParts的情况
					tempPart.Position = pos
					tempPart.Transparency = 1
					tempPart.CanCollide = false
					placeItem(item, tempPart, item:IsA("Tool"))
					AddItems(item, itemData)
					tempPart:Destroy()
				end
			end
		end
	end

	-- 无建筑时的怪物生成（保持不变）
	if monsterId and not buildingId then  
		local monsters = ParseEventId(monsterId) 
		if monsters then
			for _, monsterInfo in pairs(monsters) do  
				for _ = 1, math.random(monsterInfo.minNum, monsterInfo.maxNum) do
					local targetMonsterId = SelectIdByWeight(monsterInfo.targets) 
					local monsterData = monsterConfig:GetDataById(targetMonsterId)  
					if monsterData then
						local spawner = monsterManager.new() 
						spawner:SpawnMonster(monsterData.Id, pos, false)
					end
				end
			end
		end
	end
end

Init()

-- 外部调用添加标签（保持不变）
function EventEntity:AddTy(obj, data)
	if not obj or not data then
		warn("AddTy 缺少必要参数: obj 或 data")
		return
	end
	AddItems(obj, data)
end

return EventEntity