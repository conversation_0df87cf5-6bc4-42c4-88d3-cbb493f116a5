--[[
远程武器跟随功能综合测试脚本
测试武器跟随鼠标转动功能和碰撞修复效果
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local WeaponFollowTest = {}

-- 测试配置
local TEST_CONFIG = {
    testWeapons = {"HyperlaserGun", "MP5K"}, -- 测试用远程武器
    testDuration = 30, -- 测试持续时间（秒）
    positionCheckInterval = 0.2, -- 位置检查间隔（秒）
    maxAllowedMovement = 1.0, -- 最大允许的角色移动距离
    rotationTestAngles = {
        {pitch = 30, yaw = 0, name = "向上30度"},
        {pitch = -30, yaw = 0, name = "向下30度"},
        {pitch = 0, yaw = 45, name = "向右45度"},
        {pitch = 0, yaw = -45, name = "向左45度"},
        {pitch = 60, yaw = 30, name = "向上60度向右30度"},
        {pitch = -45, yaw = -30, name = "向下45度向左30度"}
    }
}

-- 测试状态
local testState = {
    isRunning = false,
    startTime = 0,
    currentTestPhase = 1,
    testResults = {},
    characterMovementData = {},
    weaponFollowData = {},
    lastCharacterPosition = nil,
    abnormalMovementCount = 0,
    totalChecks = 0
}

-- 测试结果
local testResults = {
    weaponEquipTest = false,
    followActivationTest = false,
    rotationTests = {},
    followStopTest = false,
    collisionTest = true, -- 默认通过，除非检测到异常
    overallSuccess = false
}

-- 初始化测试系统
function WeaponFollowTest:Initialize()
    print("🧪 远程武器跟随功能综合测试系统已加载")
    print("📋 测试内容:")
    print("  1. 武器装备和跟随激活测试")
    print("  2. 武器跟随鼠标转动测试")
    print("  3. 武器碰撞和角色移动测试")
    print("  4. 武器跟随停止测试")
    print("")
    print("🔑 快捷键:")
    print("  F9 - 开始综合测试")
    print("  F10 - 停止测试")
    print("  F11 - 查看测试结果")
    
    self:SetupHotkeys()
    
    -- 初始化相关服务
    WeaponClient:Initialize()
    CameraControlService:Initialize()
end

-- 设置快捷键
function WeaponFollowTest:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F9 then
            self:StartComprehensiveTest()
        elseif input.KeyCode == Enum.KeyCode.F10 then
            self:StopTest()
        elseif input.KeyCode == Enum.KeyCode.F11 then
            self:ShowTestResults()
        end
    end)
end

-- 开始综合测试
function WeaponFollowTest:StartComprehensiveTest()
    if testState.isRunning then
        print("⚠️ 测试已在运行中")
        return
    end
    
    print("🚀 开始远程武器跟随功能综合测试")
    print("=" * 50)
    
    -- 重置测试状态
    testState.isRunning = true
    testState.startTime = tick()
    testState.currentTestPhase = 1
    testState.abnormalMovementCount = 0
    testState.totalChecks = 0
    testResults = {
        weaponEquipTest = false,
        followActivationTest = false,
        rotationTests = {},
        followStopTest = false,
        collisionTest = true,
        overallSuccess = false
    }
    
    -- 记录初始角色位置
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        testState.lastCharacterPosition = player.Character.HumanoidRootPart.Position
    end
    
    -- 开始测试序列
    self:RunTestSequence()
end

-- 运行测试序列
function WeaponFollowTest:RunTestSequence()
    task.spawn(function()
        -- 阶段1：武器装备和跟随激活测试
        print("\n📋 阶段1：武器装备和跟随激活测试")
        local equipSuccess = self:TestWeaponEquipAndFollow()
        
        if not equipSuccess then
            print("❌ 武器装备测试失败，终止测试")
            self:StopTest()
            return
        end
        
        -- 阶段2：武器跟随转动测试
        print("\n📋 阶段2：武器跟随转动测试")
        self:TestWeaponRotationFollow()
        
        -- 阶段3：碰撞和移动测试（在转动测试期间并行进行）
        print("\n📋 阶段3：碰撞和移动监控（并行进行）")
        self:StartCollisionMonitoring()
        
        -- 等待测试完成
        task.wait(TEST_CONFIG.testDuration)
        
        -- 阶段4：武器跟随停止测试
        print("\n📋 阶段4：武器跟随停止测试")
        self:TestWeaponFollowStop()
        
        -- 生成最终报告
        self:GenerateFinalReport()
        
        testState.isRunning = false
    end)
end

-- 测试1：武器装备和跟随激活
function WeaponFollowTest:TestWeaponEquipAndFollow()
    print("  🔧 装备远程武器...")
    
    -- 确保第一人称视角
    CameraControlService:SetFirstPersonView()
    task.wait(1)
    
    -- 装备第一个测试武器
    if not self:EquipTestWeapon(TEST_CONFIG.testWeapons[1]) then
        return false
    end
    
    task.wait(2) -- 等待装备完成
    
    -- 检查武器是否正确装备
    testResults.weaponEquipTest = WeaponClient.IsWeaponEquipped and WeaponClient.RemoteData ~= nil
    
    -- 检查武器跟随是否激活
    testResults.followActivationTest = CameraControlService:IsWeaponFollowActive()
    
    print("  ✅ 武器装备状态: " .. (testResults.weaponEquipTest and "成功" or "失败"))
    print("  ✅ 跟随激活状态: " .. (testResults.followActivationTest and "成功" or "失败"))
    
    return testResults.weaponEquipTest and testResults.followActivationTest
end

-- 装备测试武器
function WeaponFollowTest:EquipTestWeapon(weaponName)
    local player = Players.LocalPlayer
    local backpack = player:FindFirstChild("Backpack")
    
    if not backpack then
        print("❌ 找不到背包")
        return false
    end
    
    local weapon = backpack:FindFirstChild(weaponName)
    if not weapon then
        print("❌ 找不到测试武器: " .. weaponName)
        print("💡 可用武器:")
        for _, child in pairs(backpack:GetChildren()) do
            if child:IsA("Tool") then
                print("  - " .. child.Name)
            end
        end
        return false
    end
    
    -- 装备武器
    weapon.Parent = player.Character
    print("  ✅ 已装备武器: " .. weaponName)
    return true
end

-- 测试2：武器跟随转动
function WeaponFollowTest:TestWeaponRotationFollow()
    print("  🔄 测试武器跟随鼠标转动...")
    
    local camera = workspace.CurrentCamera
    if not camera then
        print("❌ 相机不存在")
        return
    end
    
    -- 记录初始相机角度
    local initialCFrame = camera.CFrame
    
    -- 测试各种角度
    for i, angleTest in ipairs(TEST_CONFIG.rotationTestAngles) do
        print("    测试角度 " .. i .. ": " .. angleTest.name)
        
        -- 计算目标相机角度
        local pitchRad = math.rad(angleTest.pitch)
        local yawRad = math.rad(angleTest.yaw)
        
        -- 应用旋转
        local targetCFrame = initialCFrame * CFrame.Angles(pitchRad, yawRad, 0)
        camera.CFrame = targetCFrame
        
        -- 等待武器跟随
        task.wait(1)
        
        -- 检查武器是否跟随（这里简化为检查跟随系统是否仍然激活）
        local followActive = CameraControlService:IsWeaponFollowActive()
        testResults.rotationTests[i] = {
            angle = angleTest,
            success = followActive,
            timestamp = tick() - testState.startTime
        }
        
        print("      结果: " .. (followActive and "✅ 成功" or "❌ 失败"))
    end
    
    -- 恢复初始相机角度
    camera.CFrame = initialCFrame
    print("  ✅ 武器转动测试完成")
end

-- 开始碰撞监控
function WeaponFollowTest:StartCollisionMonitoring()
    print("  👁️ 开始监控角色异常移动...")
    
    local lastCheckTime = tick()
    
    local monitorConnection = RunService.Heartbeat:Connect(function()
        if not testState.isRunning then
            monitorConnection:Disconnect()
            return
        end
        
        local currentTime = tick()
        if currentTime - lastCheckTime < TEST_CONFIG.positionCheckInterval then
            return
        end
        
        lastCheckTime = currentTime
        self:CheckCharacterMovement()
    end)
end

-- 检查角色移动
function WeaponFollowTest:CheckCharacterMovement()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local currentPosition = player.Character.HumanoidRootPart.Position
    testState.totalChecks = testState.totalChecks + 1
    
    if testState.lastCharacterPosition then
        local movement = (currentPosition - testState.lastCharacterPosition).Magnitude
        
        if movement > TEST_CONFIG.maxAllowedMovement then
            testState.abnormalMovementCount = testState.abnormalMovementCount + 1
            print("    ⚠️ 检测到异常移动: " .. string.format("%.3f", movement) .. " 单位")
            
            -- 记录异常移动数据
            table.insert(testState.characterMovementData, {
                time = tick() - testState.startTime,
                movement = movement,
                position = currentPosition
            })
        end
    end
    
    testState.lastCharacterPosition = currentPosition
end

-- 测试3：武器跟随停止
function WeaponFollowTest:TestWeaponFollowStop()
    print("  🛑 测试武器跟随停止...")
    
    -- 卸载武器
    local player = Players.LocalPlayer
    if player.Character then
        for _, child in pairs(player.Character:GetChildren()) do
            if child:IsA("Tool") then
                child.Parent = player.Backpack
                print("    ✅ 已卸载武器: " .. child.Name)
                break
            end
        end
    end
    
    task.wait(1)
    
    -- 检查跟随是否停止
    testResults.followStopTest = not CameraControlService:IsWeaponFollowActive()
    print("  ✅ 跟随停止状态: " .. (testResults.followStopTest and "成功" or "失败"))
end

-- 停止测试
function WeaponFollowTest:StopTest()
    if not testState.isRunning then
        return
    end
    
    print("🛑 手动停止测试")
    testState.isRunning = false
    
    -- 清理
    CameraControlService:StopWeaponCameraFollow()
    
    -- 生成报告
    self:GenerateFinalReport()
end

-- 生成最终报告
function WeaponFollowTest:GenerateFinalReport()
    print("\n" .. "=" * 60)
    print("📊 远程武器跟随功能综合测试报告")
    print("=" * 60)
    
    local testDuration = tick() - testState.startTime
    local abnormalRate = testState.totalChecks > 0 and (testState.abnormalMovementCount / testState.totalChecks * 100) or 0
    
    print("🕐 测试时长: " .. string.format("%.1f", testDuration) .. " 秒")
    print("🔍 总位置检查次数: " .. testState.totalChecks)
    print("⚠️ 异常移动次数: " .. testState.abnormalMovementCount)
    print("📈 异常移动率: " .. string.format("%.2f", abnormalRate) .. "%")
    print("")
    
    -- 各项测试结果
    print("📋 详细测试结果:")
    print("  1. 武器装备测试: " .. (testResults.weaponEquipTest and "✅ 通过" or "❌ 失败"))
    print("  2. 跟随激活测试: " .. (testResults.followActivationTest and "✅ 通过" or "❌ 失败"))
    
    -- 转动测试结果
    local rotationSuccessCount = 0
    for i, result in ipairs(testResults.rotationTests) do
        if result.success then
            rotationSuccessCount = rotationSuccessCount + 1
        end
    end
    local rotationSuccessRate = #testResults.rotationTests > 0 and (rotationSuccessCount / #testResults.rotationTests * 100) or 0
    print("  3. 转动跟随测试: " .. rotationSuccessCount .. "/" .. #testResults.rotationTests .. " (" .. string.format("%.1f", rotationSuccessRate) .. "% 成功率)")
    
    print("  4. 跟随停止测试: " .. (testResults.followStopTest and "✅ 通过" or "❌ 失败"))
    
    -- 碰撞测试结果
    testResults.collisionTest = abnormalRate < 10 -- 异常移动率低于10%视为通过
    print("  5. 碰撞测试: " .. (testResults.collisionTest and "✅ 通过" or "❌ 失败") .. " (异常率: " .. string.format("%.2f", abnormalRate) .. "%)")
    
    -- 总体评估
    testResults.overallSuccess = testResults.weaponEquipTest and 
                                testResults.followActivationTest and 
                                rotationSuccessRate >= 80 and 
                                testResults.followStopTest and 
                                testResults.collisionTest
    
    print("")
    if testResults.overallSuccess then
        print("🎉 总体测试结果: ✅ 通过")
        print("💡 远程武器跟随功能工作正常，可以投入使用！")
    else
        print("❌ 总体测试结果: 失败")
        print("💡 需要进一步调试和修复")
    end
    
    print("=" * 60)
end

-- 显示测试结果
function WeaponFollowTest:ShowTestResults()
    if testState.isRunning then
        print("⚠️ 测试正在进行中，请等待测试完成")
        return
    end
    
    self:GenerateFinalReport()
end

-- 自动初始化
WeaponFollowTest:Initialize()

return WeaponFollowTest
