--[[
武器跟随导致人物飞起来问题诊断脚本
专门诊断WeldConstraint + 锚定followPart导致角色飞起来的问题
]]

local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
local UserInputService = game:GetService("UserInputService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- 引入相关服务
local WeaponClient = require(ReplicatedStorage.Scripts.Client.Services.WeaponClient)
local CameraControlService = require(ReplicatedStorage.Scripts.Client.Services.CameraControlService)

local FlyingDiagnostic = {}

-- 诊断状态
local diagnosticState = {
    isMonitoring = false,
    startTime = 0,
    characterPositions = {},
    characterVelocities = {},
    constraintForces = {},
    monitorConnection = nil,
    lastCharacterPosition = nil,
    flyingDetected = false,
    maxHeight = 0,
    initialHeight = 0
}

-- 诊断配置
local DIAGNOSTIC_CONFIG = {
    monitorDuration = 15, -- 监控持续时间（秒）
    flyingThreshold = 5.0, -- 飞行检测阈值（高度变化）
    velocityThreshold = 20.0, -- 异常速度阈值
    testWeapons = {"HyperlaserGun", "MP5K"}
}

-- 初始化诊断系统
function FlyingDiagnostic:Initialize()
    print("🚁 武器跟随飞行问题诊断系统已加载")
    print("📋 诊断功能:")
    print("  1. 监控角色高度变化")
    print("  2. 检测异常速度和加速度")
    print("  3. 分析WeldConstraint的物理影响")
    print("  4. 提供详细的修复建议")
    print("")
    print("🔑 快捷键:")
    print("  F5 - 开始飞行问题诊断")
    print("  Ctrl+F5 - 停止诊断")
    
    self:SetupHotkeys()
end

-- 设置快捷键
function FlyingDiagnostic:SetupHotkeys()
    UserInputService.InputBegan:Connect(function(input, gameProcessed)
        if gameProcessed then return end
        
        if input.KeyCode == Enum.KeyCode.F5 then
            if UserInputService:IsKeyDown(Enum.KeyCode.LeftControl) then
                self:StopDiagnostic()
            else
                self:StartDiagnostic()
            end
        end
    end)
end

-- 开始诊断
function FlyingDiagnostic:StartDiagnostic()
    if diagnosticState.isMonitoring then
        print("⚠️ 诊断已在进行中")
        return
    end
    
    print("🚀 开始武器跟随飞行问题诊断")
    print("=" * 50)
    
    -- 重置状态
    diagnosticState.isMonitoring = true
    diagnosticState.startTime = tick()
    diagnosticState.characterPositions = {}
    diagnosticState.characterVelocities = {}
    diagnosticState.constraintForces = {}
    diagnosticState.flyingDetected = false
    diagnosticState.maxHeight = 0
    
    -- 记录初始状态
    local player = Players.LocalPlayer
    if player.Character and player.Character:FindFirstChild("HumanoidRootPart") then
        diagnosticState.lastCharacterPosition = player.Character.HumanoidRootPart.Position
        diagnosticState.initialHeight = diagnosticState.lastCharacterPosition.Y
        diagnosticState.maxHeight = diagnosticState.initialHeight
        self:RecordCharacterState("INITIAL")
    end
    
    -- 开始监控循环
    diagnosticState.monitorConnection = RunService.Heartbeat:Connect(function()
        self:MonitorCharacterPhysics()
        self:MonitorConstraintForces()
    end)
    
    -- 自动停止诊断
    task.spawn(function()
        task.wait(DIAGNOSTIC_CONFIG.monitorDuration)
        if diagnosticState.isMonitoring then
            self:StopDiagnostic()
        end
    end)
    
    print("📊 诊断已启动，将持续 " .. DIAGNOSTIC_CONFIG.monitorDuration .. " 秒")
    print("💡 现在装备一个远程武器并观察角色行为...")
end

-- 停止诊断
function FlyingDiagnostic:StopDiagnostic()
    if not diagnosticState.isMonitoring then
        print("⚠️ 诊断未在进行中")
        return
    end
    
    print("🛑 停止诊断")
    diagnosticState.isMonitoring = false
    
    -- 断开连接
    if diagnosticState.monitorConnection then
        diagnosticState.monitorConnection:Disconnect()
        diagnosticState.monitorConnection = nil
    end
    
    -- 生成诊断报告
    self:GenerateDiagnosticReport()
end

-- 监控角色物理状态
function FlyingDiagnostic:MonitorCharacterPhysics()
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    local humanoidRootPart = player.Character.HumanoidRootPart
    local currentPosition = humanoidRootPart.Position
    local currentTime = tick()
    
    if diagnosticState.lastCharacterPosition then
        local movement = currentPosition - diagnosticState.lastCharacterPosition
        local timeDelta = 0.1 -- 大约的时间间隔
        local velocity = movement / timeDelta
        
        -- 记录速度
        table.insert(diagnosticState.characterVelocities, {
            time = currentTime - diagnosticState.startTime,
            velocity = velocity,
            speed = velocity.Magnitude,
            position = currentPosition
        })
        
        -- 检测飞行
        local heightChange = currentPosition.Y - diagnosticState.initialHeight
        if heightChange > diagnosticState.maxHeight - diagnosticState.initialHeight then
            diagnosticState.maxHeight = currentPosition.Y
        end
        
        if heightChange > DIAGNOSTIC_CONFIG.flyingThreshold then
            if not diagnosticState.flyingDetected then
                diagnosticState.flyingDetected = true
                self:RecordCharacterState("FLYING_DETECTED")
                print("🚁 检测到角色飞行! 高度变化: " .. string.format("%.3f", heightChange) .. " 单位")
            end
        end
        
        -- 检测异常速度
        if velocity.Magnitude > DIAGNOSTIC_CONFIG.velocityThreshold then
            self:RecordCharacterState("HIGH_VELOCITY")
            print("⚡ 检测到异常速度: " .. string.format("%.3f", velocity.Magnitude) .. " 单位/秒")
        end
    end
    
    diagnosticState.lastCharacterPosition = currentPosition
end

-- 监控约束力
function FlyingDiagnostic:MonitorConstraintForces()
    local player = Players.LocalPlayer
    if not player.Character then
        return
    end
    
    -- 检查装备的武器和相关约束
    for _, child in pairs(player.Character:GetChildren()) do
        if child:IsA("Tool") then
            local handle = child:FindFirstChild("Handle")
            if handle then
                self:AnalyzeHandleConstraints(handle, child.Name)
            end
        end
    end
    
    -- 检查workspace中的跟随部件
    local followPart = workspace:FindFirstChild("WeaponFollowPart")
    if followPart then
        self:AnalyzeFollowPart(followPart)
    end
end

-- 分析Handle约束
function FlyingDiagnostic:AnalyzeHandleConstraints(handle, weaponName)
    local currentTime = tick() - diagnosticState.startTime
    
    -- 检查WeldConstraint
    for _, constraint in pairs(handle:GetChildren()) do
        if constraint:IsA("WeldConstraint") then
            table.insert(diagnosticState.constraintForces, {
                time = currentTime,
                weaponName = weaponName,
                constraintType = "WeldConstraint",
                part0 = constraint.Part0 and constraint.Part0.Name or "nil",
                part1 = constraint.Part1 and constraint.Part1.Name or "nil",
                part0Anchored = constraint.Part0 and constraint.Part0.Anchored or false,
                part1Anchored = constraint.Part1 and constraint.Part1.Anchored or false
            })
        end
    end
end

-- 分析跟随部件
function FlyingDiagnostic:AnalyzeFollowPart(followPart)
    local currentTime = tick() - diagnosticState.startTime
    
    table.insert(diagnosticState.constraintForces, {
        time = currentTime,
        partType = "FollowPart",
        anchored = followPart.Anchored,
        position = followPart.Position,
        cframe = followPart.CFrame
    })
end

-- 记录角色状态
function FlyingDiagnostic:RecordCharacterState(eventType)
    local player = Players.LocalPlayer
    if not player.Character or not player.Character:FindFirstChild("HumanoidRootPart") then
        return
    end
    
    table.insert(diagnosticState.characterPositions, {
        time = tick() - diagnosticState.startTime,
        eventType = eventType,
        position = player.Character.HumanoidRootPart.Position,
        height = player.Character.HumanoidRootPart.Position.Y
    })
end

-- 生成诊断报告
function FlyingDiagnostic:GenerateDiagnosticReport()
    print("\n" .. "=" * 60)
    print("🚁 武器跟随飞行问题诊断报告")
    print("=" * 60)
    
    local diagnosticDuration = tick() - diagnosticState.startTime
    local maxHeightChange = diagnosticState.maxHeight - diagnosticState.initialHeight
    
    print("🕐 诊断时长: " .. string.format("%.2f", diagnosticDuration) .. " 秒")
    print("📍 位置记录数: " .. #diagnosticState.characterPositions)
    print("⚡ 速度记录数: " .. #diagnosticState.characterVelocities)
    print("🔗 约束记录数: " .. #diagnosticState.constraintForces)
    print("")
    
    -- 飞行检测结果
    print("🚁 飞行检测结果:")
    print("  初始高度: " .. string.format("%.3f", diagnosticState.initialHeight))
    print("  最大高度: " .. string.format("%.3f", diagnosticState.maxHeight))
    print("  高度变化: " .. string.format("%.3f", maxHeightChange) .. " 单位")
    print("  飞行阈值: " .. DIAGNOSTIC_CONFIG.flyingThreshold .. " 单位")
    
    if diagnosticState.flyingDetected then
        print("  结果: 🚨 检测到角色飞行!")
    else
        print("  结果: ✅ 未检测到飞行")
    end
    print("")
    
    -- 速度分析
    if #diagnosticState.characterVelocities > 0 then
        local maxSpeed = 0
        local avgSpeed = 0
        local totalSpeed = 0
        
        for _, vel in ipairs(diagnosticState.characterVelocities) do
            if vel.speed > maxSpeed then
                maxSpeed = vel.speed
            end
            totalSpeed = totalSpeed + vel.speed
        end
        
        avgSpeed = totalSpeed / #diagnosticState.characterVelocities
        
        print("⚡ 速度分析:")
        print("  最大速度: " .. string.format("%.3f", maxSpeed) .. " 单位/秒")
        print("  平均速度: " .. string.format("%.3f", avgSpeed) .. " 单位/秒")
        print("  异常阈值: " .. DIAGNOSTIC_CONFIG.velocityThreshold .. " 单位/秒")
        
        if maxSpeed > DIAGNOSTIC_CONFIG.velocityThreshold then
            print("  结果: ⚡ 检测到异常速度!")
        else
            print("  结果: ✅ 速度正常")
        end
        print("")
    end
    
    -- 约束分析
    print("🔗 约束分析:")
    local weldConstraints = 0
    local anchoredParts = 0
    
    for _, constraint in ipairs(diagnosticState.constraintForces) do
        if constraint.constraintType == "WeldConstraint" then
            weldConstraints = weldConstraints + 1
            print("  WeldConstraint: " .. constraint.part0 .. " <-> " .. constraint.part1)
            print("    Part0锚定: " .. tostring(constraint.part0Anchored))
            print("    Part1锚定: " .. tostring(constraint.part1Anchored))
            
            if constraint.part0Anchored or constraint.part1Anchored then
                anchoredParts = anchoredParts + 1
            end
        elseif constraint.partType == "FollowPart" then
            print("  FollowPart锚定: " .. tostring(constraint.anchored))
            if constraint.anchored then
                anchoredParts = anchoredParts + 1
            end
        end
    end
    
    print("  WeldConstraint数量: " .. weldConstraints)
    print("  锚定部件数量: " .. anchoredParts)
    print("")
    
    -- 问题分析和修复建议
    print("🔍 问题分析:")
    if diagnosticState.flyingDetected or maxHeightChange > DIAGNOSTIC_CONFIG.flyingThreshold then
        print("❌ 检测到飞行问题")
        print("💡 问题原因:")
        print("  1. WeldConstraint连接了锚定的followPart和非锚定的Handle")
        print("  2. 锚定的followPart被强制移动时，通过WeldConstraint拖拽Handle")
        print("  3. Handle作为Tool的一部分，影响整个角色的物理状态")
        print("  4. 角色被武器的物理约束'拖拽'到空中")
        
        print("\n🔧 修复建议:")
        print("  1. 完全避免锚定Handle或与Handle相关的部件")
        print("  2. 使用BodyPosition/BodyVelocity控制武器位置")
        print("  3. 或者使用完全独立的武器显示系统")
        print("  4. 确保武器物理系统不影响角色物理")
    else
        print("✅ 未检测到明显的飞行问题")
    end
    
    print("=" * 60)
    print("📋 诊断完成")
end

-- 自动初始化
FlyingDiagnostic:Initialize()

return FlyingDiagnostic
